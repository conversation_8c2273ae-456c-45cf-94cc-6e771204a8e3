{"name": "streamit-pro-nextjs", "version": "3.0.0", "description": "Professional video conferencing platform built with Next.js, TypeScript, TailwindCSS, and WebRTC", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:next\"", "dev:next": "next dev", "dev:server": "tsx watch server/index.ts", "build": "next build && tsc --project tsconfig.server.json", "start": "node dist/server/index.js", "lint": "next lint", "type-check": "tsc --noEmit"}, "keywords": ["video-conferencing", "webrtc", "typescript", "nextjs", "tailwindcss", "shadcn-ui", "zustand", "socket.io", "recording", "chat", "screen-sharing"], "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/joelgriiyo/streamit2.git"}, "homepage": "https://github.com/joelgriiyo/streamit2", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.0.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "lucide-react": "^0.294.0", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-slot": "^1.0.2", "tailwindcss-animate": "^1.0.7", "zustand": "^4.4.0", "socket.io-client": "^4.8.1", "express": "^4.18.2", "socket.io": "^4.8.1", "cors": "^2.8.5"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "tsx": "^4.0.0", "concurrently": "^8.2.0"}}