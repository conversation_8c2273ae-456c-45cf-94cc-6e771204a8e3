{"name": "streamit-pro-vanilla-ts", "version": "3.0.0", "description": "Professional multi-participant video conferencing platform with WebRTC, noise cancellation, and real-time communication", "main": "dist/server.js", "type": "module", "scripts": {"start": "node dist/server.js", "build": "tsc && vite build", "dev": "vite", "type-check": "tsc --noEmit", "heroku-postbuild": "npm run build"}, "keywords": ["video-conferencing", "webrtc", "typescript", "vanilla-js", "recording", "chat", "screen-sharing", "background-effects", "professional", "meeting", "multi-participant", "noise-cancellation"], "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/joelgriiyo/streamit2.git"}, "homepage": "https://github.com/joelgriiyo/streamit2", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "vite": "^5.0.0", "@types/express": "^4.17.21"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}