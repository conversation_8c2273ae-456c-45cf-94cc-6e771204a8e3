/* Main styles for StreamIt Pro */
:root {
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    --background-color: #f8fafc;
    --text-color: #1e293b;
    --border-color: #e2e8f0;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-color);
}

#app {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

header {
    background-color: var(--primary-color);
    color: white;
    padding: 1rem;
    text-align: center;
}

header h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

main {
    flex: 1;
    padding: 1rem;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

#meeting-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

#local-video-container {
    position: relative;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

#local-video {
    width: 100%;
    height: auto;
    display: block;
}

#remote-videos {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

#controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

button {
    padding: 0.5rem 1rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

button:hover {
    background-color: var(--secondary-color);
}

button:disabled {
    background-color: #94a3b8;
    cursor: not-allowed;
}

footer {
    text-align: center;
    padding: 1rem;
    background-color: var(--primary-color);
    color: white;
    margin-top: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #controls {
        flex-wrap: wrap;
    }
    
    button {
        flex: 1 0 calc(50% - 0.5rem);
    }
}
