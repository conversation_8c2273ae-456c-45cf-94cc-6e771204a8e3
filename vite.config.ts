import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  root: 'temp-ts',
  publicDir: '../public',
  build: {
    outDir: '../dist',
    emptyOutDir: true,
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'public/index.html'),
      },
    },
  },
  server: {
    port: 3000,
    open: true,
    host: true,
    cors: true
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'temp-ts')
    }
  }
});
