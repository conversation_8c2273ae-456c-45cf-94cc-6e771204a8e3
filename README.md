# StreamIt Pro - Next.js Video Conferencing Platform

<div align="center">

**🎥 Professional Video Conferencing Platform Built with Next.js & TypeScript**

[![GitHub](https://img.shields.io/badge/GitHub-Repository-black?style=for-the-badge&logo=github)](https://github.com/joelgriiyo/streamit2)

---

### 🚀 **Modern Tech Stack - Zero Docker Required!**

</div>

## 🌟 **Overview**

**StreamIt Pro** is a modern, professional-grade video conferencing platform built with **Next.js 14**, **TypeScript**, **TailwindCSS**, and **WebRTC**. It provides **enterprise-level meeting experiences** with **high-quality video**, **real-time communication**, and **advanced collaboration features**.

## ✨ **Key Features**

- **🎥 High-Definition Video Conferencing** - Crystal clear 1080p video with adaptive quality
- **🎤 Professional Audio** - Advanced noise cancellation and echo reduction
- **👥 Multi-Participant Support** - Host meetings with multiple participants seamlessly
- **💬 Real-Time Chat** - Integrated messaging with modern UI
- **🖥️ Screen Sharing** - Share your entire screen or specific applications
- **📱 Responsive Design** - Works perfectly on desktop, tablet, and mobile devices
- **🔒 Secure & Private** - End-to-end encrypted communications via WebRTC
- **⚡ Lightning Fast** - Optimized with Next.js for maximum performance

## 🛠️ **Technology Stack**

### **Frontend Technologies**
- **Next.js 14** - React framework with App Router
- **React 18** - Modern React with hooks and concurrent features
- **TypeScript 5** - Type-safe development
- **TailwindCSS 3** - Utility-first CSS framework
- **ShadCN UI** - Beautiful, accessible UI components
- **Zustand** - Lightweight state management

### **Backend Technologies**
- **Node.js 18** - JavaScript runtime
- **Express.js 4** - Web application framework
- **Socket.IO 4** - Real-time bidirectional communication
- **TypeScript** - Type-safe server development

### **WebRTC & Real-Time**
- **WebRTC API** - Peer-to-peer video/audio communication
- **MediaDevices API** - Camera and microphone access
- **Screen Capture API** - Screen sharing capabilities

## 🚀 **Quick Start**

### **Prerequisites**
- Node.js 18.0.0 or higher
- npm 9.0.0 or higher

### **Installation & Development**

```bash
# Clone the repository
git clone https://github.com/joelgriiyo/streamit2.git
cd streamit2

# Install dependencies
npm install

# Start development servers (both frontend and backend)
npm run dev

# Or start them separately:
npm run dev:next    # Next.js frontend on http://localhost:3000
npm run dev:server  # Express server on http://localhost:3002
```

### **Production Build**

```bash
# Build the application
npm run build

# Start production server
npm start
```

## 🏗️ **Project Structure**

```
streamit2/
├── app/                    # Next.js App Router
│   ├── globals.css        # Global styles with Tailwind
│   ├── layout.tsx         # Root layout
│   ├── page.tsx          # Home page
│   └── room/[id]/        # Dynamic room pages
├── components/            # React components
│   ├── ui/               # ShadCN UI components
│   └── VideoCall/        # Video call components
├── lib/                  # Utility libraries
│   ├── store.ts          # Zustand state management
│   ├── socket.ts         # Socket.IO client
│   ├── rtc.ts           # WebRTC utilities
│   └── utils.ts         # General utilities
├── server/               # Express backend
│   └── index.ts         # Socket.IO server
├── next.config.js       # Next.js configuration
├── tailwind.config.js   # TailwindCSS configuration
└── tsconfig.json        # TypeScript configuration
```

## 🎮 **How to Use**

### **🚀 Starting a Meeting**

1. **🌐 Visit**: `http://localhost:3000`
2. **👤 Enter**: Your name
3. **🎯 Choose**: "Start New Meeting"
4. **🎥 Allow**: Camera/microphone permissions
5. **🔗 Share**: Room ID with participants

### **🔗 Joining a Meeting**

1. **🌐 Visit**: `http://localhost:3000`
2. **👤 Enter**: Your name
3. **🔑 Input**: Room ID from host
4. **🎥 Allow**: Camera/microphone permissions
5. **🚀 Join**: The video conference!

### **⚙️ Meeting Controls**

- **🎤 Mute/Unmute**: Toggle microphone
- **📹 Camera**: Turn video on/off
- **🖥️ Screen Share**: Share your screen
- **💬 Chat**: Open messaging panel
- **👥 Participants**: View participant list
- **⚙️ Settings**: Adjust preferences
- **📞 Leave**: Exit the meeting

## 🔧 **Configuration**

### **Environment Variables**

Create a `.env.local` file in the root directory:

```env
# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:3000

# Server Port
PORT=3002

# Node Environment
NODE_ENV=development
```

## 📊 **Features in Detail**

### **🎥 Video Quality**
- **HD Quality**: 1280x720 at 30fps (default)
- **Full HD**: 1920x1080 at 30fps (configurable)
- **Adaptive Bitrate**: Automatically adjusts based on network conditions
- **Multiple Participants**: Optimized grid layout for up to 9 participants

### **🎤 Audio Features**
- **Echo Cancellation**: Built-in browser echo cancellation
- **Noise Suppression**: Advanced noise reduction
- **Auto Gain Control**: Consistent audio levels
- **High-Quality Audio**: 48kHz sample rate

### **💬 Real-Time Chat**
- **Instant Messaging**: Real-time chat with Socket.IO
- **Message History**: Persistent chat during session
- **Unread Indicators**: Visual notification for new messages
- **Responsive Design**: Mobile-optimized chat interface

### **🖥️ Screen Sharing**
- **Full Screen**: Share entire desktop
- **Application Window**: Share specific applications
- **High Resolution**: Up to 4K screen sharing support
- **Audio Sharing**: Include system audio in screen share

## 🔒 **Security & Privacy**

| Security Feature | Implementation | Benefit |
|:---:|:---:|:---:|
| **End-to-End Encryption** | WebRTC DTLS/SRTP | 🔐 Secure Communication |
| **Private Rooms** | Unique Room IDs | 🏠 Controlled Access |
| **No Data Storage** | Peer-to-Peer Direct | 🛡️ Privacy Protection |
| **CORS Protection** | Express CORS Middleware | 🔒 Secure API Access |

## 🌐 **Browser Compatibility**

| Browser | Desktop | Mobile | WebRTC Support |
|:---:|:---:|:---:|:---:|
| **Chrome** | ✅ Full | ✅ Full | ✅ Excellent |
| **Firefox** | ✅ Full | ✅ Full | ✅ Excellent |
| **Safari** | ✅ Full | ✅ Full | ✅ Good |
| **Edge** | ✅ Full | ✅ Limited | ✅ Good |

## 🚀 **Deployment**

### **Vercel (Recommended for Frontend)**

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel
```

### **Railway/Render (For Full-Stack)**

1. Connect your GitHub repository
2. Set environment variables
3. Deploy both frontend and backend

## 🤝 **Contributing**

We welcome contributions! Please follow these steps:

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

## 📄 **License**

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for details.

---

<div align="center">

## 🌟 **Ready to Start?**

```bash
npm install && npm run dev
```

**Experience modern video conferencing with Next.js!**

---

**Made with ❤️ by [Joel](https://github.com/joelgriiyo)**

</div>
