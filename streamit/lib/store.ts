import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

// Export all types
export type Participant = {
  id: string
  name: string
  stream?: MediaStream
  isAudioMuted: boolean
  isVideoMuted: boolean
  isScreenSharing: boolean
  role: 'host' | 'co-host' | 'participant'
  isHandRaised: boolean
  joinedAt: Date
  lastActivity: Date
}

export type ChatMessage = {
  id: string
  userId: string
  userName: string
  message: string
  timestamp: Date
  isSpam?: boolean
  isBlocked?: boolean
}

export type SecuritySettings = {
  encryptionEnabled: boolean
  antiSpamEnabled: boolean
  maxMessagesPerMinute: number
  allowScreenShare: boolean
  allowFileSharing: boolean
  requireApprovalToJoin: boolean
}

export type AdminControls = {
  canMuteAll: boolean
  canMuteParticipant: boolean
  canRemoveParticipant: boolean
  canControlCamera: boolean
  canManageRoles: boolean
}

export type VideoCallState = {
  // Room state
  roomId: string | null
  isConnected: boolean
  roomLocked: boolean
  currentUser: Participant | null
  participants: Map<string, Participant>
  localStream: MediaStream | null
  isAudioMuted: boolean
  isVideoMuted: boolean
  isScreenSharing: boolean
  messages: ChatMessage[]
  unreadCount: number
  messageHistory: Map<string, number>
  isChatOpen: boolean
  isSettingsOpen: boolean
  securitySettings: SecuritySettings
  adminControls: AdminControls
  blockedUsers: Set<string>
  spamDetection: Map<string, { count: number; lastReset: number }>

  // Actions
  setRoomId: (roomId: string | null) => void
  setConnected: (isConnected: boolean) => void
  setCurrentUser: (user: Participant | null) => void
  addParticipant: (participant: Participant) => void
  removeParticipant: (participantId: string) => void
  updateParticipant: (participantId: string, updates: Partial<Participant>) => void
  setLocalStream: (stream: MediaStream | null) => void
  toggleAudio: () => void
  toggleVideo: () => void
  toggleScreenShare: () => void
  addMessage: (message: ChatMessage) => void
  clearUnreadCount: () => void
  toggleChat: () => void
  toggleSettings: () => void
  muteParticipant: (participantId: string) => void
  muteAllParticipants: () => void
  removeParticipantAsAdmin: (participantId: string) => void
  promoteToCoHost: (participantId: string) => void
  demoteFromCoHost: (participantId: string) => void
  toggleRoomLock: () => void
  blockUser: (userId: string) => void
  unblockUser: (userId: string) => void
  updateSecuritySettings: (settings: Partial<SecuritySettings>) => void
  detectSpam: (userId: string) => boolean
  reset: () => void
}

const initialState = {
  roomId: null,
  isConnected: false,
  roomLocked: false,
  currentUser: null,
  participants: new Map<string, NonNullable<VideoCallState['currentUser']>>(),
  localStream: null,
  isAudioMuted: false,
  isVideoMuted: false,
  isScreenSharing: false,
  messages: [],
  unreadCount: 0,
  messageHistory: new Map<string, number>(),
  isChatOpen: false,
  isSettingsOpen: false,
  securitySettings: {
    encryptionEnabled: true,
    antiSpamEnabled: true,
    maxMessagesPerMinute: 10,
    allowScreenShare: true,
    allowFileSharing: true,
    requireApprovalToJoin: false
  },
  adminControls: {
    canMuteAll: true,
    canMuteParticipant: true,
    canRemoveParticipant: true,
    canControlCamera: true,
    canManageRoles: true
  },
  blockedUsers: new Set<string>(),
  spamDetection: new Map<string, { count: number; lastReset: number }>()
}

// Create store with SSR support
const useVideoCallStore = create<VideoCallState>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      setRoomId: (roomId) => set({ roomId }),
      
      setConnected: (isConnected) => set({ isConnected }),
      
      setCurrentUser: (currentUser) => set({ currentUser }),
      
      addParticipant: (participant) =>
        set((state) => {
          const newParticipants = new Map(state.participants)
          newParticipants.set(participant.id, participant)
          return { participants: newParticipants }
        }),
      
      removeParticipant: (participantId) =>
        set((state) => {
          const newParticipants = new Map(state.participants)
          newParticipants.delete(participantId)
          return { participants: newParticipants }
        }),
      
      updateParticipant: (participantId, updates) =>
        set((state) => {
          const participant = state.participants.get(participantId)
          if (!participant) return state
          
          const newParticipants = new Map(state.participants)
          newParticipants.set(participantId, { ...participant, ...updates })
          return { participants: newParticipants }
        }),
      
      setLocalStream: (localStream) => set({ localStream }),
      
      toggleAudio: () =>
        set((state) => {
          const newMuted = !state.isAudioMuted
          if (state.localStream) {
            state.localStream.getAudioTracks().forEach((track) => {
              track.enabled = !newMuted
            })
          }
          return { isAudioMuted: newMuted }
        }),
      
      toggleVideo: () =>
        set((state) => {
          const newMuted = !state.isVideoMuted
          if (state.localStream) {
            state.localStream.getVideoTracks().forEach((track) => {
              track.enabled = !newMuted
            })
          }
          return { isVideoMuted: newMuted }
        }),
      
      toggleScreenShare: () =>
        set((state) => ({
          isScreenSharing: !state.isScreenSharing
        })),
      
      addMessage: (message) =>
        set((state) => {
          // Check for spam if anti-spam is enabled
          let isSpam = false
          if (state.securitySettings.antiSpamEnabled) {
            const now = Date.now()
            const userSpam = state.spamDetection.get(message.userId) || {
              count: 0,
              lastReset: now
            }

            // Reset count if more than a minute has passed
            if (now - userSpam.lastReset > 60000) {
              userSpam.count = 0
              userSpam.lastReset = now
            }

            userSpam.count++
            state.spamDetection.set(message.userId, userSpam)

            // Mark as spam if exceeding limit
            isSpam = userSpam.count > state.securitySettings.maxMessagesPerMinute
          }

          return {
            messages: [...state.messages, { ...message, isSpam }],
            unreadCount: state.isChatOpen ? state.unreadCount : state.unreadCount + 1
          }
        }),
      
      clearUnreadCount: () => set({ unreadCount: 0 }),
      
      toggleChat: () =>
        set((state) => ({
          isChatOpen: !state.isChatOpen,
          unreadCount: 0
        })),
      
      toggleSettings: () =>
        set((state) => ({
          isSettingsOpen: !state.isSettingsOpen
        })),
      
      // Admin Actions
      muteParticipant: (participantId) =>
        set((state) => {
          const participant = state.participants.get(participantId)
          if (!participant) return state
          
          const newParticipants = new Map(state.participants)
          newParticipants.set(participantId, {
            ...participant,
            isAudioMuted: true
          })
          return { participants: newParticipants }
        }),
      
      muteAllParticipants: () =>
        set((state) => {
          const newParticipants = new Map()
          state.participants.forEach((participant) => {
            newParticipants.set(participant.id, {
              ...participant,
              isAudioMuted: true
            })
          })
          return { participants: newParticipants }
        }),
      
      removeParticipantAsAdmin: (participantId) =>
        set((state) => {
          const newParticipants = new Map(state.participants)
          newParticipants.delete(participantId)
          return { participants: newParticipants }
        }),
      
      promoteToCoHost: (participantId) =>
        set((state) => {
          const participant = state.participants.get(participantId)
          if (!participant) return state
          
          const newParticipants = new Map(state.participants)
          newParticipants.set(participantId, {
            ...participant,
            role: 'co-host'
          })
          return { participants: newParticipants }
        }),
      
      demoteFromCoHost: (participantId) =>
        set((state) => {
          const participant = state.participants.get(participantId)
          if (!participant) return state
          
          const newParticipants = new Map(state.participants)
          newParticipants.set(participantId, {
            ...participant,
            role: 'participant'
          })
          return { participants: newParticipants }
        }),
      
      toggleRoomLock: () =>
        set((state) => ({
          roomLocked: !state.roomLocked
        })),
      
      blockUser: (userId) =>
        set((state) => {
          const blockedUsers = new Set(state.blockedUsers)
          blockedUsers.add(userId)
          return { blockedUsers }
        }),
      
      unblockUser: (userId) =>
        set((state) => {
          const blockedUsers = new Set(state.blockedUsers)
          blockedUsers.delete(userId)
          return { blockedUsers }
        }),
      
      updateSecuritySettings: (settings) =>
        set((state) => ({
          securitySettings: {
            ...state.securitySettings,
            ...settings
          }
        })),
      
      detectSpam: (userId) => {
        const state = get()
        if (!state.securitySettings.antiSpamEnabled) return false

        const userSpam = state.spamDetection.get(userId)
        if (!userSpam) return false

        const now = Date.now()
        if (now - userSpam.lastReset > 60000) return false

        return userSpam.count > state.securitySettings.maxMessagesPerMinute
      },
      
      reset: () => set(initialState)
    }),
    {
      name: 'video-call-store',
      enabled: typeof window !== 'undefined' && process.env.NODE_ENV === 'development'
    }
  )
)

// Export the store hook
export default useVideoCallStore

// Export the store type for context
export type VideoCallStore = ReturnType<typeof useVideoCallStore>
