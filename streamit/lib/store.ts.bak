import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

export interface Participant {
  id: string
  name: string
  stream?: MediaStream
  isAudioMuted: boolean
  isVideoMuted: boolean
  isScreenSharing: boolean
  role: 'host' | 'co-host' | 'participant'
  isHandRaised: boolean
  joinedAt: Date
  lastActivity: Date
}

export interface ChatMessage {
  id: string
  userId: string
  userName: string
  message: string
  timestamp: Date
  isSpam?: boolean
  isBlocked?: boolean
}

export interface SecuritySettings {
  encryptionEnabled: boolean
  antiSpamEnabled: boolean
  maxMessagesPerMinute: number
  allowScreenShare: boolean
  allowFileSharing: boolean
  requireApprovalToJoin: boolean
}

export interface AdminControls {
  canMuteAll: boolean
  canMuteParticipant: boolean
  canRemoveParticipant: boolean
  canControlCamera: boolean
  canManageRoles: boolean
}

export interface VideoCallState {
  // Room state
  roomId: string | null
  isConnected: boolean
  roomLocked: boolean

  // User state
  currentUser: Participant | null
  participants: Map<string, Participant>

  // Media state
  localStream: MediaStream | null
  isAudioMuted: boolean
  isVideoMuted: boolean
  isScreenSharing: boolean

  // Chat state
  messages: ChatMessage[]
  unreadCount: number
  messageHistory: Map<string, number> // userId -> message count in last minute

  // UI state
  isChatOpen: boolean
  isSettingsOpen: boolean

  // Security & Admin
  securitySettings: SecuritySettings
  adminControls: AdminControls
  blockedUsers: Set<string>
  spamDetection: Map<string, { count: number, lastReset: number }>
  
  // Actions
  setRoomId: (roomId: string) => void
  setConnected: (connected: boolean) => void
  setCurrentUser: (user: Participant) => void
  addParticipant: (participant: Participant) => void
  removeParticipant: (participantId: string) => void
  updateParticipant: (participantId: string, updates: Partial<Participant>) => void
  setLocalStream: (stream: MediaStream | null) => void
  toggleAudio: () => void
  toggleVideo: () => void
  toggleScreenShare: () => void
  addMessage: (message: ChatMessage) => void
  clearUnreadCount: () => void
  toggleChat: () => void
  toggleSettings: () => void

  // Admin Actions
  muteParticipant: (participantId: string) => void
  muteAllParticipants: () => void
  removeParticipantAsAdmin: (participantId: string) => void
  promoteToCoHost: (participantId: string) => void
  demoteFromCoHost: (participantId: string) => void
  toggleRoomLock: () => void
  blockUser: (userId: string) => void
  unblockUser: (userId: string) => void

  // Security Actions
  updateSecuritySettings: (settings: Partial<SecuritySettings>) => void
  detectSpam: (userId: string) => boolean

  reset: () => void
}

const initialState = {
  roomId: null,
  isConnected: false,
  roomLocked: false,
  currentUser: null,
  participants: new Map(),
  localStream: null,
  isAudioMuted: false,
  isVideoMuted: false,
  isScreenSharing: false,
  messages: [],
  unreadCount: 0,
  messageHistory: new Map(),
  isChatOpen: false,
  isSettingsOpen: false,
  securitySettings: {
    encryptionEnabled: true,
    antiSpamEnabled: true,
    maxMessagesPerMinute: 10,
    allowScreenShare: true,
    allowFileSharing: true,
    requireApprovalToJoin: false,
  },
  adminControls: {
    canMuteAll: true,
    canMuteParticipant: true,
    canRemoveParticipant: true,
    canControlCamera: true,
    canManageRoles: true,
  },
  blockedUsers: new Set<string>(),
  spamDetection: new Map<string, { count: number, lastReset: number }>(),
}

export const useVideoCallStore = create<VideoCallState>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      setRoomId: (roomId) => set({ roomId }),
      
      setConnected: (isConnected) => set({ isConnected }),
      
      setCurrentUser: (currentUser) => set({ currentUser }),
      
      addParticipant: (participant) => set((state) => {
        const newParticipants = new Map(state.participants)
        newParticipants.set(participant.id, participant)
        return { participants: newParticipants }
      }),
      
      removeParticipant: (participantId) => set((state) => {
        const newParticipants = new Map(state.participants)
        newParticipants.delete(participantId)
        return { participants: newParticipants }
      }),
      
      updateParticipant: (participantId, updates) => set((state) => {
        const newParticipants = new Map(state.participants)
        const participant = newParticipants.get(participantId)
        if (participant) {
          newParticipants.set(participantId, { ...participant, ...updates })
        }
        return { participants: newParticipants }
      }),
      
      setLocalStream: (localStream) => set({ localStream }),
      
      toggleAudio: () => set((state) => {
        const newMuted = !state.isAudioMuted
        if (state.localStream) {
          state.localStream.getAudioTracks().forEach(track => {
            track.enabled = !newMuted
          })
        }
        return { isAudioMuted: newMuted }
      }),
      
      toggleVideo: () => set((state) => {
        const newMuted = !state.isVideoMuted
        if (state.localStream) {
          state.localStream.getVideoTracks().forEach(track => {
            track.enabled = !newMuted
          })
        }
        return { isVideoMuted: newMuted }
      }),
      
      toggleScreenShare: () => set((state) => ({
        isScreenSharing: !state.isScreenSharing
      })),
      
      addMessage: (message) => set((state) => {
        // Check for spam if anti-spam is enabled
        if (state.securitySettings.antiSpamEnabled) {
          const now = Date.now()
          const userSpam = state.spamDetection.get(message.userId) || { count: 0, lastReset: now }

          // Reset count if more than a minute has passed
          if (now - userSpam.lastReset > 60000) {
            userSpam.count = 0
            userSpam.lastReset = now
          }

          userSpam.count++
          state.spamDetection.set(message.userId, userSpam)

          // Mark as spam if exceeding limit
          if (userSpam.count > state.securitySettings.maxMessagesPerMinute) {
            message.isSpam = true
          }
        }

        return {
          messages: [...state.messages, message],
          unreadCount: state.isChatOpen ? state.unreadCount : state.unreadCount + 1
        }
      }),

      clearUnreadCount: () => set({ unreadCount: 0 }),

      toggleChat: () => set((state) => ({
        isChatOpen: !state.isChatOpen,
        unreadCount: !state.isChatOpen ? 0 : state.unreadCount
      })),

      toggleSettings: () => set((state) => ({
        isSettingsOpen: !state.isSettingsOpen
      })),

      // Admin Actions
      muteParticipant: (participantId) => set((state) => {
        const participant = state.participants.get(participantId)
        if (participant && (state.currentUser?.role === 'host' || state.currentUser?.role === 'co-host')) {
          const newParticipants = new Map(state.participants)
          newParticipants.set(participantId, { ...participant, isAudioMuted: true })
          return { participants: newParticipants }
        }
        return state
      }),

      muteAllParticipants: () => set((state) => {
        if (state.currentUser?.role === 'host' || state.currentUser?.role === 'co-host') {
          const newParticipants = new Map()
          state.participants.forEach((participant, id) => {
            newParticipants.set(id, { ...participant, isAudioMuted: true })
          })
          return { participants: newParticipants }
        }
        return state
      }),

      removeParticipantAsAdmin: (participantId) => set((state) => {
        if (state.currentUser?.role === 'host' || state.currentUser?.role === 'co-host') {
          const newParticipants = new Map(state.participants)
          newParticipants.delete(participantId)
          return { participants: newParticipants }
        }
        return state
      }),

      promoteToCoHost: (participantId) => set((state) => {
        const participant = state.participants.get(participantId)
        if (participant && state.currentUser?.role === 'host') {
          const newParticipants = new Map(state.participants)
          newParticipants.set(participantId, { ...participant, role: 'co-host' })
          return { participants: newParticipants }
        }
        return state
      }),

      demoteFromCoHost: (participantId) => set((state) => {
        const participant = state.participants.get(participantId)
        if (participant && state.currentUser?.role === 'host') {
          const newParticipants = new Map(state.participants)
          newParticipants.set(participantId, { ...participant, role: 'participant' })
          return { participants: newParticipants }
        }
        return state
      }),

      toggleRoomLock: () => set((state) => {
        if (state.currentUser?.role === 'host') {
          return { roomLocked: !state.roomLocked }
        }
        return state
      }),

      blockUser: (userId) => set((state) => {
        if (state.currentUser?.role === 'host' || state.currentUser?.role === 'co-host') {
          const newBlockedUsers = new Set(state.blockedUsers)
          newBlockedUsers.add(userId)
          return { blockedUsers: newBlockedUsers }
        }
        return state
      }),

      unblockUser: (userId) => set((state) => {
        if (state.currentUser?.role === 'host' || state.currentUser?.role === 'co-host') {
          const newBlockedUsers = new Set(state.blockedUsers)
          newBlockedUsers.delete(userId)
          return { blockedUsers: newBlockedUsers }
        }
        return state
      }),

      // Security Actions
      updateSecuritySettings: (settings) => set((state) => {
        if (state.currentUser?.role === 'host') {
          return {
            securitySettings: { ...state.securitySettings, ...settings }
          }
        }
        return state
      }),
  )

// Create a store instance for the server
let store: Store

// Initialize the store on the client side
const initializeStore = (preloadedState: Partial<VideoCallState> = {}) => {
  // Check if we're on the server or client
  const isServer = typeof window === 'undefined'
  
  // If on the server, create a new store
  if (isServer) {
    return createStore()
  }
  
  // On the client, reuse the store or create a new one
  if (!store) {
    store = createStore()
  }
  
  // If there's preloaded state, update the store
  if (preloadedState) {
    store.setState({
      ...store.getState(),
      ...preloadedState,
    })
  }
  
  return store
}

// Export the store with SSR support
export const useVideoCallStore = (preloadedState?: Partial<VideoCallState>) => {
  const isServer = typeof window === 'undefined'
  const store = initializeStore(preloadedState)
  
  // On the server, we'll return a new store instance for each request
  if (isServer) {
    return store
  }
  
  // On the client, we'll use the same store instance
  return store
}
