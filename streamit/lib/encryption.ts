// Video Encryption Utilities for StreamIt Pro
// Provides end-to-end encryption for video and audio streams

export class StreamEncryption {
  private static instance: StreamEncryption
  private encryptionKey: CryptoKey | null = null
  private isEnabled: boolean = true

  private constructor() {}

  static getInstance(): StreamEncryption {
    if (!StreamEncryption.instance) {
      StreamEncryption.instance = new StreamEncryption()
    }
    return StreamEncryption.instance
  }

  // Generate encryption key for the session
  async generateEncryptionKey(): Promise<void> {
    try {
      this.encryptionKey = await window.crypto.subtle.generateKey(
        {
          name: 'AES-GCM',
          length: 256,
        },
        true,
        ['encrypt', 'decrypt']
      )
      console.log('🔐 Encryption key generated successfully')
    } catch (error) {
      console.error('Failed to generate encryption key:', error)
      this.isEnabled = false
    }
  }

  // Encrypt video frame data
  async encryptVideoFrame(frameData: ArrayBuffer): Promise<ArrayBuffer | null> {
    if (!this.isEnabled || !this.encryptionKey) {
      return frameData // Return original if encryption disabled
    }

    try {
      const iv = window.crypto.getRandomValues(new Uint8Array(12))
      const encrypted = await window.crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv: iv,
        },
        this.encryptionKey,
        frameData
      )

      // Combine IV and encrypted data
      const result = new ArrayBuffer(iv.length + encrypted.byteLength)
      const resultView = new Uint8Array(result)
      resultView.set(iv, 0)
      resultView.set(new Uint8Array(encrypted), iv.length)

      return result
    } catch (error) {
      console.error('Video encryption failed:', error)
      return frameData
    }
  }

  // Decrypt video frame data
  async decryptVideoFrame(encryptedData: ArrayBuffer): Promise<ArrayBuffer | null> {
    if (!this.isEnabled || !this.encryptionKey) {
      return encryptedData // Return original if encryption disabled
    }

    try {
      const iv = encryptedData.slice(0, 12)
      const encrypted = encryptedData.slice(12)

      const decrypted = await window.crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: iv,
        },
        this.encryptionKey,
        encrypted
      )

      return decrypted
    } catch (error) {
      console.error('Video decryption failed:', error)
      return null
    }
  }

  // Encrypt audio data
  async encryptAudioData(audioData: ArrayBuffer): Promise<ArrayBuffer | null> {
    if (!this.isEnabled || !this.encryptionKey) {
      return audioData
    }

    try {
      const iv = window.crypto.getRandomValues(new Uint8Array(12))
      const encrypted = await window.crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv: iv,
        },
        this.encryptionKey,
        audioData
      )

      const result = new ArrayBuffer(iv.length + encrypted.byteLength)
      const resultView = new Uint8Array(result)
      resultView.set(iv, 0)
      resultView.set(new Uint8Array(encrypted), iv.length)

      return result
    } catch (error) {
      console.error('Audio encryption failed:', error)
      return audioData
    }
  }

  // Decrypt audio data
  async decryptAudioData(encryptedData: ArrayBuffer): Promise<ArrayBuffer | null> {
    if (!this.isEnabled || !this.encryptionKey) {
      return encryptedData
    }

    try {
      const iv = encryptedData.slice(0, 12)
      const encrypted = encryptedData.slice(12)

      const decrypted = await window.crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: iv,
        },
        this.encryptionKey,
        encrypted
      )

      return decrypted
    } catch (error) {
      console.error('Audio decryption failed:', error)
      return null
    }
  }

  // Enable/disable encryption
  setEncryptionEnabled(enabled: boolean): void {
    this.isEnabled = enabled
    console.log(`🔐 Encryption ${enabled ? 'enabled' : 'disabled'}`)
  }

  // Check if encryption is available and enabled
  isEncryptionEnabled(): boolean {
    return this.isEnabled && this.encryptionKey !== null
  }

  // Get encryption status for UI
  getEncryptionStatus(): { enabled: boolean; keyGenerated: boolean } {
    return {
      enabled: this.isEnabled,
      keyGenerated: this.encryptionKey !== null
    }
  }

  // Export key for sharing with other participants (in real implementation)
  async exportKey(): Promise<ArrayBuffer | null> {
    if (!this.encryptionKey) return null

    try {
      return await window.crypto.subtle.exportKey('raw', this.encryptionKey)
    } catch (error) {
      console.error('Failed to export encryption key:', error)
      return null
    }
  }

  // Import key from another participant (in real implementation)
  async importKey(keyData: ArrayBuffer): Promise<void> {
    try {
      this.encryptionKey = await window.crypto.subtle.importKey(
        'raw',
        keyData,
        { name: 'AES-GCM' },
        true,
        ['encrypt', 'decrypt']
      )
      console.log('🔐 Encryption key imported successfully')
    } catch (error) {
      console.error('Failed to import encryption key:', error)
      this.isEnabled = false
    }
  }
}

// DDoS Protection Utilities
export class DDoSProtection {
  private static instance: DDoSProtection
  private connectionAttempts: Map<string, { count: number; lastAttempt: number }> = new Map()
  private blockedIPs: Set<string> = new Set()
  private maxAttemptsPerMinute: number = 10
  private blockDuration: number = 5 * 60 * 1000 // 5 minutes

  private constructor() {}

  static getInstance(): DDoSProtection {
    if (!DDoSProtection.instance) {
      DDoSProtection.instance = new DDoSProtection()
    }
    return DDoSProtection.instance
  }

  // Check if connection should be allowed
  checkConnectionAllowed(clientIP: string): boolean {
    const now = Date.now()

    // Check if IP is blocked
    if (this.blockedIPs.has(clientIP)) {
      return false
    }

    // Get or create connection attempt record
    const attempts = this.connectionAttempts.get(clientIP) || { count: 0, lastAttempt: now }

    // Reset count if more than a minute has passed
    if (now - attempts.lastAttempt > 60000) {
      attempts.count = 0
      attempts.lastAttempt = now
    }

    attempts.count++
    this.connectionAttempts.set(clientIP, attempts)

    // Block if too many attempts
    if (attempts.count > this.maxAttemptsPerMinute) {
      this.blockedIPs.add(clientIP)
      console.warn(`🚫 Blocked IP ${clientIP} for excessive connection attempts`)
      
      // Unblock after duration
      setTimeout(() => {
        this.blockedIPs.delete(clientIP)
        this.connectionAttempts.delete(clientIP)
        console.log(`✅ Unblocked IP ${clientIP}`)
      }, this.blockDuration)

      return false
    }

    return true
  }

  // Manually block an IP
  blockIP(clientIP: string): void {
    this.blockedIPs.add(clientIP)
    console.warn(`🚫 Manually blocked IP ${clientIP}`)
  }

  // Manually unblock an IP
  unblockIP(clientIP: string): void {
    this.blockedIPs.delete(clientIP)
    this.connectionAttempts.delete(clientIP)
    console.log(`✅ Manually unblocked IP ${clientIP}`)
  }

  // Get protection status
  getProtectionStatus(): { blockedIPs: number; activeConnections: number } {
    return {
      blockedIPs: this.blockedIPs.size,
      activeConnections: this.connectionAttempts.size
    }
  }

  // Configure protection settings
  configure(maxAttemptsPerMinute: number, blockDurationMinutes: number): void {
    this.maxAttemptsPerMinute = maxAttemptsPerMinute
    this.blockDuration = blockDurationMinutes * 60 * 1000
    console.log(`🛡️ DDoS protection configured: ${maxAttemptsPerMinute} attempts/min, ${blockDurationMinutes}min block`)
  }
}

// Initialize encryption on module load
export const streamEncryption = StreamEncryption.getInstance()
export const ddosProtection = DDoSProtection.getInstance()

// Auto-generate encryption key
streamEncryption.generateEncryptionKey()
