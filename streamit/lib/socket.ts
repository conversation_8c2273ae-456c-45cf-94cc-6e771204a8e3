import { io, Socket } from 'socket.io-client'

class SocketManager {
  private socket: Socket | null = null
  private roomId: string | null = null

  connect() {
    if (this.socket?.connected) return this.socket

    this.socket = io(process.env.NODE_ENV === 'production'
      ? window.location.origin
      : 'http://localhost:3002', {
      transports: ['websocket', 'polling'],
      upgrade: true,
    })

    this.socket.on('connect', () => {
      console.log('Connected to server:', this.socket?.id)
    })

    this.socket.on('disconnect', () => {
      console.log('Disconnected from server')
    })

    this.socket.on('connect_error', (error) => {
      console.error('Connection error:', error)
    })

    return this.socket
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
  }

  joinRoom(roomId: string, userId: string, userName: string) {
    if (!this.socket) return

    this.roomId = roomId
    this.socket.emit('join-room', { roomId, userId, userName })
  }

  leaveRoom() {
    if (!this.socket || !this.roomId) return

    this.socket.emit('leave-room', { roomId: this.roomId })
    this.roomId = null
  }

  sendSignal(targetUserId: string, signal: any) {
    if (!this.socket || !this.roomId) return

    this.socket.emit('signal', {
      roomId: this.roomId,
      targetUserId,
      signal
    })
  }

  sendChatMessage(message: string, userName: string) {
    if (!this.socket || !this.roomId) return

    this.socket.emit('chat-message', {
      roomId: this.roomId,
      message,
      userName,
      timestamp: new Date().toISOString()
    })
  }

  on(event: string, callback: (...args: any[]) => void) {
    if (!this.socket) return

    this.socket.on(event, callback)
  }

  off(event: string, callback?: (...args: any[]) => void) {
    if (!this.socket) return

    this.socket.off(event, callback)
  }

  getSocket() {
    return this.socket
  }

  isConnected() {
    return this.socket?.connected || false
  }
}

export const socketManager = new SocketManager()
