/** @type {import('next').NextConfig} */
const nextConfig = {
  typescript: {
    ignoreBuildErrors: false,
  },
  eslint: {
    ignoreDuringBuilds: false,
  },
  images: {
    domains: [],
  },
  async rewrites() {
    return [
      {
        source: '/api/socket.io/:path*',
        destination: process.env.NODE_ENV === 'production' 
          ? 'https://your-heroku-app.herokuapp.com/socket.io/:path*'
          : 'http://localhost:3002/socket.io/:path*',
      },
    ];
  },
};

export default nextConfig;
