'use client'

import { useEffect, useRef } from 'react'
import { Participant } from '@/lib/store'
import { Mi<PERSON>, MicOff, Video, VideoOff, Monitor, Crown } from 'lucide-react'

interface VideoTileProps {
  participant: Participant
  stream?: MediaStream | null
  isLocal: boolean
  isFeatured?: boolean
}

export function VideoTile({ participant, stream, isLocal, isFeatured = false }: VideoTileProps) {
  const videoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    if (videoRef.current && stream) {
      videoRef.current.srcObject = stream
    }
  }, [stream])

  const hasVideo = stream && stream.getVideoTracks().length > 0 && !participant.isVideoMuted
  const hasAudio = stream && stream.getAudioTracks().length > 0 && !participant.isAudioMuted

  const tileSize = isFeatured ? 'w-full h-full' : 'w-full h-full max-w-sm max-h-64'
  const avatarSize = isFeatured ? 'w-24 h-24' : 'w-16 h-16'
  const textSize = isFeatured ? 'text-xl' : 'text-lg'
  const nameSize = isFeatured ? 'text-lg' : 'text-sm'

  return (
    <div className={`video-container relative overflow-hidden ${tileSize}`}>
      {hasVideo ? (
        <video
          ref={videoRef}
          autoPlay
          playsInline
          muted={isLocal} // Mute local video to prevent feedback
          className="w-full h-full object-cover rounded-lg"
        />
      ) : (
        <div className="w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center rounded-lg">
          <div className="text-center">
            <div className={`${avatarSize} bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg`}>
              <span className={`text-white ${textSize} font-bold`}>
                {participant.name.charAt(0).toUpperCase()}
              </span>
            </div>
            <p className={`text-white ${nameSize} font-medium`}>{participant.name}</p>
            <p className="text-white/60 text-xs">Camera is off</p>
          </div>
        </div>
      )}

      {/* Participant info overlay */}
      <div className="absolute bottom-2 left-2 right-2 flex items-center justify-between">
        <div className="glass-dark px-2 py-1 rounded-lg">
          <div className="flex items-center gap-1">
            {isLocal && <Crown className="h-3 w-3 text-yellow-400" />}
            <span className="text-white text-xs font-medium">
              {isLocal ? 'You' : participant.name}
            </span>
          </div>
        </div>

        <div className="flex items-center space-x-1">
          {/* Audio indicator */}
          <div className={`p-1 rounded-full ${hasAudio ? 'bg-green-500/80' : 'bg-red-500/80'} backdrop-blur-sm`}>
            {hasAudio ? (
              <Mic className="h-3 w-3 text-white" />
            ) : (
              <MicOff className="h-3 w-3 text-white" />
            )}
          </div>

          {/* Video indicator */}
          <div className={`p-1 rounded-full ${hasVideo ? 'bg-green-500/80' : 'bg-red-500/80'} backdrop-blur-sm`}>
            {hasVideo ? (
              <Video className="h-3 w-3 text-white" />
            ) : (
              <VideoOff className="h-3 w-3 text-white" />
            )}
          </div>

          {/* Screen sharing indicator */}
          {participant.isScreenSharing && (
            <div className="p-1 rounded-full bg-blue-500/80 backdrop-blur-sm">
              <Monitor className="h-3 w-3 text-white" />
            </div>
          )}
        </div>
      </div>

      {/* Local user indicator */}
      {isLocal && (
        <div className="absolute top-2 left-2">
          <div className="glass-dark px-2 py-1 rounded-lg">
            <span className="text-white text-xs font-medium flex items-center gap-1">
              <Crown className="h-2 w-2 text-yellow-400" />
              Host
            </span>
          </div>
        </div>
      )}

      {/* Connection quality indicator */}
      <div className="absolute top-2 right-2">
        <div className="flex space-x-0.5">
          <div className="w-0.5 h-2 bg-green-400 rounded-full"></div>
          <div className="w-0.5 h-3 bg-green-400 rounded-full"></div>
          <div className="w-0.5 h-4 bg-green-400 rounded-full"></div>
        </div>
      </div>
    </div>
  )
}
