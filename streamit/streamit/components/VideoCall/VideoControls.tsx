'use client'

import { useState, useEffect, useCallback } from 'react'
import { useVideoCallStoreContext } from '@/providers/StoreProvider'
import { rtcManager } from '@/lib/rtc'
import { SettingsModal } from './SettingsModal'
import {
  Mic,
  MicOff,
  Video,
  VideoOff,
  Monitor,
  MonitorOff,
  Settings as SettingsIcon,
  PhoneOff,
  ChevronUp,
  Camera,
  Headphones
} from 'lucide-react'

interface MediaDevice {
  deviceId: string
  label: string
  kind: MediaDeviceKind
}

export function VideoControls() {
  const [isScreenSharingState, setIsScreenSharing] = useState(false)
  const [notification, setNotification] = useState<string | null>(null)
  const [showCameraDropdown, setShowCameraDropdown] = useState(false)
  const [showMicDropdown, setShowMicDropdown] = useState(false)
  const [showSettingsModal, setShowSettingsModal] = useState(false)
  const [devices, setDevices] = useState<MediaDevice[]>([])
  const [selectedCamera, setSelectedCamera] = useState('')
  const [selectedMicrophone, setSelectedMicrophone] = useState('')

  const toggleAudio = useVideoCallStoreContext((state) => state.toggleAudio)
  const toggleVideo = useVideoCallStoreContext((state) => state.toggleVideo)
  const toggleScreenShare = useVideoCallStoreContext((state) => state.toggleScreenShare)
  const isAudioMuted = useVideoCallStoreContext((state) => state.isAudioMuted)
  const isVideoMuted = useVideoCallStoreContext((state) => state.isVideoMuted)
  const isScreenSharing = useVideoCallStoreContext((state) => state.isScreenSharing)
  const currentUser = useVideoCallStoreContext((state) => state.currentUser)

  // Load available devices
  const loadDevices = useCallback(async () => {
    try {
      // Request permissions first to get non-empty device labels
      await navigator.mediaDevices.getUserMedia({ audio: true, video: true })
      const deviceList = await navigator.mediaDevices.enumerateDevices()
      
      const mediaDevices: MediaDevice[] = deviceList
        .filter(device => device.kind === 'audioinput' || device.kind === 'videoinput')
        .map(device => ({
          deviceId: device.deviceId,
          label: device.label || `${device.kind} ${device.deviceId.slice(0, 8)}`,
          kind: device.kind as MediaDeviceKind
        }))
        
      setDevices(mediaDevices)

      // Set default devices if not already set
      if (!selectedCamera) {
        const defaultCamera = mediaDevices.find(d => d.kind === 'videoinput')
        if (defaultCamera) setSelectedCamera(defaultCamera.deviceId)
      }
      
      if (!selectedMicrophone) {
        const defaultMic = mediaDevices.find(d => d.kind === 'audioinput')
        if (defaultMic) setSelectedMicrophone(defaultMic.deviceId)
      }
    } catch (error) {
      console.error('Error loading devices:', error)
    }
  }, [selectedCamera, selectedMicrophone])

  useEffect(() => {
    loadDevices()
    
    // Add event listener for device changes
    navigator.mediaDevices.addEventListener('devicechange', loadDevices)
    
    return () => {
      navigator.mediaDevices.removeEventListener('devicechange', loadDevices)
    }
  }, [loadDevices])

  const showNotification = (message: string) => {
    setNotification(message)
    setTimeout(() => setNotification(null), 2000)
  }

  const handleToggleAudio = () => {
    toggleAudio()
    showNotification(isAudioMuted ? 'Microphone unmuted' : 'Microphone muted')
  }

  const handleToggleVideo = () => {
    toggleVideo()
    showNotification(isVideoMuted ? 'Camera turned on' : 'Camera turned off')
  }

  const handleToggleScreenShare = useCallback(async () => {
    try {
      if (!isScreenSharing) {
        // Start screen sharing
        const screenStream = await rtcManager.getDisplayMedia()

        // Replace video track with screen share
        rtcManager.replaceVideoTrack(screenStream)

        // Listen for screen share end
        screenStream.getVideoTracks()[0].onended = () => {
          handleStopScreenShare()
        }

        toggleScreenShare()
        showNotification('Screen sharing started')
      } else {
        handleStopScreenShare()
      }
    } catch (error) {
      console.error('Error toggling screen share:', error)
      showNotification('Failed to toggle screen share')
    }
  }, [isScreenSharing, toggleScreenShare])

  const handleStopScreenShare = useCallback(async () => {
    try {
      // Switch back to camera
      rtcManager.restoreVideoTrack()
      toggleScreenShare()
      showNotification('Screen sharing stopped')
    } catch (error) {
      console.error('Error stopping screen share:', error)
      showNotification('Failed to stop screen share')
    }
  }, [toggleScreenShare])

  const handleCameraSelect = useCallback(async (deviceId: string) => {
    if (deviceId === selectedCamera) {
      setShowCameraDropdown(false)
      return
    }
    
    try {
      await rtcManager.switchCamera(deviceId)
      setSelectedCamera(deviceId)
      setShowCameraDropdown(false)
      showNotification('Camera changed')
    } catch (error) {
      console.error('Error switching camera:', error)
      showNotification('Failed to switch camera')
    }
  }, [selectedCamera])

  const handleMicrophoneSelect = useCallback(async (deviceId: string) => {
    if (deviceId === selectedMicrophone) {
      setShowMicDropdown(false)
      return
    }
    
    try {
      // For now, we'll just update the selected microphone
      // The actual device switching will be handled by the RTC manager
      setSelectedMicrophone(deviceId)
      setShowMicDropdown(false)
      showNotification('Microphone changed')
    } catch (error) {
      console.error('Error switching microphone:', error)
      showNotification('Failed to switch microphone')
    }
  }, [selectedMicrophone, isAudioMuted])

  const cameras = devices.filter((d: MediaDevice) => d.kind === 'videoinput')
  const microphones = devices.filter((d: MediaDevice) => d.kind === 'audioinput')

  return (
    <div className="p-4">
      <div className="video-controls">
        {/* Audio controls with dropdown */}
        <div className="relative">
          <div className="flex">
            <button
              onClick={handleToggleAudio}
              className={`control-btn ${isAudioMuted ? 'active' : 'inactive'} rounded-r-none`}
              title={isAudioMuted ? 'Unmute microphone' : 'Mute microphone'}
            >
              {isAudioMuted ? (
                <MicOff className="h-6 w-6" />
              ) : (
                <Mic className="h-6 w-6" />
              )}
            </button>
            {microphones.length > 1 && (
              <button
                onClick={() => setShowMicDropdown(!showMicDropdown)}
                className="control-btn inactive rounded-l-none border-l border-white/20 w-8"
                title="Select microphone"
              >
                <ChevronUp className="h-4 w-4" />
              </button>
            )}
          </div>

          {showMicDropdown && microphones.length > 1 && (
            <div className="absolute bottom-full left-0 mb-2 glass-dark rounded-lg border border-white/20 min-w-48 z-10">
              <div className="p-2">
                <div className="text-white text-xs font-medium mb-2 flex items-center gap-2">
                  <Headphones className="h-3 w-3" />
                  Select Microphone
                </div>
                {microphones.map((mic: MediaDevice) => (
                  <div 
                    key={mic.deviceId}
                    className="px-4 py-2 text-white hover:bg-white/10 cursor-pointer flex items-center"
                    onClick={() => handleMicrophoneSelect(mic.deviceId)}
                  >
                    <span className={selectedMicrophone === mic.deviceId ? 'font-semibold' : ''}>
                      {mic.label}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Video controls with dropdown */}
        <div className="relative">
          <div className="flex">
            <button
              onClick={handleToggleVideo}
              className={`control-btn ${isVideoMuted ? 'active' : 'inactive'} rounded-r-none`}
              title={isVideoMuted ? 'Turn on camera' : 'Turn off camera'}
            >
              {isVideoMuted ? (
                <VideoOff className="h-6 w-6" />
              ) : (
                <Video className="h-6 w-6" />
              )}
            </button>
            {cameras.length > 1 && (
              <button
                onClick={() => setShowCameraDropdown(!showCameraDropdown)}
                className="control-btn inactive rounded-l-none border-l border-white/20 w-8"
                title="Select camera"
              >
                <ChevronUp className="h-4 w-4" />
              </button>
            )}
          </div>

          {showCameraDropdown && cameras.length > 1 && (
            <div className="absolute bottom-full left-0 mb-2 glass-dark rounded-lg border border-white/20 min-w-48 z-10">
              <div className="p-2">
                <div className="text-white text-xs font-medium mb-2 flex items-center gap-2">
                  <Camera className="h-3 w-3" />
                  Select Camera
                </div>
                {cameras.map((camera: MediaDevice) => (
                  <div 
                    key={camera.deviceId}
                    className="px-4 py-2 text-white hover:bg-white/10 cursor-pointer flex items-center"
                    onClick={() => handleCameraSelect(camera.deviceId)}
                  >
                    <span className={selectedCamera === camera.deviceId ? 'font-semibold' : ''}>
                      {camera.label}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Screen share toggle */}
        <button
          onClick={handleToggleScreenShare}
          className={`control-btn ${isScreenSharing ? 'active' : 'inactive'}`}
          title={isScreenSharing ? 'Stop screen sharing' : 'Share screen'}
        >
          {isScreenSharing ? (
            <MonitorOff className="h-6 w-6" />
          ) : (
            <Monitor className="h-6 w-6" />
          )}
        </button>

        {/* Settings */}
        <button
          onClick={() => setShowSettingsModal(true)}
          className="p-2 rounded-full hover:bg-gray-200 transition-colors"
          aria-label="Settings"
        >
          <SettingsIcon className="w-6 h-6" />
        </button>
      </div>

      {/* Settings Modal */}
      <SettingsModal
        isOpen={showSettingsModal}
        onClose={() => setShowSettingsModal(false)}
      />

      {/* Notification */}
      {notification && (
        <div className="fixed bottom-20 left-1/2 transform -translate-x-1/2 glass-dark px-4 py-2 rounded-lg text-white z-50 fade-in">
          {notification}
        </div>
      )}
    </div>
  )
}
