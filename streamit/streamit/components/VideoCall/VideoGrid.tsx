'use client'

import { useVideoCallStore } from '@/lib/store'
import { VideoTile } from './VideoTile'

export function VideoGrid() {
  const { currentUser, participants, localStream } = useVideoCallStore()

  const participantsList = Array.from(participants.values())
  const totalParticipants = participantsList.length + 1 // +1 for current user

  // Calculate grid layout for smaller, more organized tiles
  const getGridClass = (count: number) => {
    if (count === 1) return 'grid-cols-1'
    if (count === 2) return 'grid-cols-1'
    if (count <= 4) return 'grid-cols-2'
    if (count <= 6) return 'grid-cols-2'
    return 'grid-cols-3'
  }

  const getGridRows = (count: number) => {
    if (count === 1) return 'grid-rows-1'
    if (count === 2) return 'grid-rows-2'
    if (count <= 4) return 'grid-rows-2'
    if (count <= 6) return 'grid-rows-3'
    return 'grid-rows-3'
  }

  return (
    <div className="flex-1 p-4">
      <div className="glass h-full p-4 overflow-hidden">
        <div className={`
          grid gap-3 h-full
          ${getGridClass(totalParticipants)}
          ${getGridRows(totalParticipants)}
          place-items-center
        `}>
          {/* Local user video - Featured if alone */}
          {currentUser && (
            <VideoTile
              key={currentUser.id}
              participant={currentUser}
              stream={localStream}
              isLocal={true}
              isFeatured={totalParticipants === 1}
            />
          )}

          {/* Remote participants */}
          {participantsList.map((participant, index) => (
            <VideoTile
              key={participant.id}
              participant={participant}
              stream={participant.stream}
              isLocal={false}
              isFeatured={totalParticipants === 2 && index === 0}
            />
          ))}
        </div>
      </div>
    </div>
  )
}
