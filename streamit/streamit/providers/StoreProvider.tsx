'use client'

import { createContext, useContext, useRef } from 'react'
import { useStore, type UseBoundStore, type StoreApi } from 'zustand'
import { useVideoCallStore, type VideoCallState } from '@/lib/store'

type StoreContextType = UseBoundStore<StoreApi<VideoCallState>> | null

export const StoreContext = createContext<StoreContextType>(null)

export function StoreProvider({
  children,
}: {
  children: React.ReactNode
}) {
  const storeRef = useRef<StoreContextType>()
  if (!storeRef.current) {
    storeRef.current = useVideoCallStore
  }

  return (
    <StoreContext.Provider value={storeRef.current}>
      {children}
    </StoreContext.Provider>
  )
}

export function useVideoCallStoreContext<T>(
  selector: (state: VideoCallState) => T,
  equalityFn?: (left: T, right: T) => boolean
): T {
  const store = useContext(StoreContext)
  if (!store) {
    throw new Error('useVideoCallStoreContext must be used within a StoreProvider')
  }
  return useStore(store, selector, equalityFn)
}
