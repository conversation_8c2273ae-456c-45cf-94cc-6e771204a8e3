{"name": "streamit-pro-nextjs", "version": "3.0.0", "type": "module", "description": "Professional video conferencing platform built with Next.js, TypeScript, TailwindCSS, and WebRTC", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:next\"", "dev:next": "next dev", "dev:server": "tsx watch server/index.mts", "build": "next build && tsc --project tsconfig.server.json --outDir dist", "start": "node --experimental-specifier-resolution=node --loader ts-node/esm dist/server/index.js", "heroku-postbuild": "npm install --production=false && npm run build", "lint": "next lint", "type-check": "tsc --noEmit"}, "keywords": ["video-conferencing", "webrtc", "typescript", "nextjs", "tailwindcss", "shadcn-ui", "zustand", "socket.io", "recording", "chat", "screen-sharing"], "author": "<PERSON>", "license": "MIT", "repository": {"type": "module", "url": "https://github.com/joelgriiyo/streamit2.git"}, "homepage": "https://github.com/joelgriiyo/streamit2", "engines": {"node": "18.x", "npm": ">=9.0.0"}, "dependencies": {"@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-toast": "^1.1.5", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "lucide-react": "^0.294.0", "next": "14.2.3", "react": "^18.2.0", "react-dom": "^18.2.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "zustand": "^4.4.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "autoprefixer": "^10.4.0", "concurrently": "^8.2.2", "eslint": "^8.57.1", "eslint-config-next": "14.2.3", "postcss": "^8.4.0", "tailwindcss": "^3.3.0", "tsx": "^4.7.0", "typescript": "^5.0.0"}}