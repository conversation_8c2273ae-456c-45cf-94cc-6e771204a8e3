import { streamEncryption } from './encryption'

// WebRTC configuration with enhanced security
const RTC_CONFIG: RTCConfiguration = {
  iceServers: [
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' },
  ],
  iceCandidatePoolSize: 10,
  bundlePolicy: 'max-bundle',
  rtcpMuxPolicy: 'require',
}

// High-quality video constraints
export const VIDEO_CONSTRAINTS: MediaStreamConstraints = {
  video: {
    width: { ideal: 1280, max: 1920 },
    height: { ideal: 720, max: 1080 },
    frameRate: { ideal: 30, max: 60 },
    facingMode: 'user'
  },
  audio: {
    echoCancellation: true,
    noiseSuppression: true,
    autoGainControl: true,
    sampleRate: 48000
  }
}

export const SCREEN_SHARE_CONSTRAINTS = {
  video: {
    width: { ideal: 1920, max: 3840 },
    height: { ideal: 1080, max: 2160 },
    frameRate: { ideal: 30, max: 60 }
  },
  audio: true
}

export class RTCManager {
  private peerConnections: Map<string, RTCPeerConnection> = new Map()
  private localStream: MediaStream | null = null
  private screenStream: MediaStream | null = null
  private cameraStream: MediaStream | null = null
  private currentVideoDeviceId: string | null = null
  private currentAudioDeviceId: string | null = null
  private onStreamCallback?: (userId: string, stream: MediaStream) => void
  private onUserDisconnectedCallback?: (userId: string) => void

  constructor() {
    this.setupEventHandlers()
  }

  private setupEventHandlers() {
    // Handle page unload (only in browser)
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.cleanup()
      })
    }
  }

  async getUserMedia(constraints: MediaStreamConstraints = VIDEO_CONSTRAINTS): Promise<MediaStream> {
    try {
      this.localStream = await navigator.mediaDevices.getUserMedia(constraints)
      this.cameraStream = this.localStream
      
      // Store device IDs if available
      const videoTracks = this.localStream.getVideoTracks()
      if (videoTracks.length > 0) {
        this.currentVideoDeviceId = videoTracks[0].getSettings().deviceId || null
      }
      
      const audioTracks = this.localStream.getAudioTracks()
      if (audioTracks.length > 0) {
        this.currentAudioDeviceId = audioTracks[0].getSettings().deviceId || null
      }
      
      return this.localStream
    } catch (error) {
      console.error('Error accessing media devices:', error)
      throw error
    }
  }
  
  // Screen sharing and media device methods
  async getDisplayMedia(constraints: MediaStreamConstraints = SCREEN_SHARE_CONSTRAINTS): Promise<MediaStream> {
    try {
      this.screenStream = await navigator.mediaDevices.getDisplayMedia(constraints)
      
      // Handle when user stops screen sharing
      this.screenStream.getVideoTracks()[0].onended = () => {
        this.restoreVideoTrack()
      }
      
      return this.screenStream
    } catch (error) {
      console.error('Error accessing screen share:', error)
      throw error
    }
  }
  
  // replaceVideoTrack is implemented below with a more comprehensive version
  
  replaceAudioTrack(stream: MediaStream) {
    if (!this.localStream) return
    
    // Stop existing audio tracks
    this.localStream.getAudioTracks().forEach(track => track.stop())
    
    // Add new audio track
    const audioTrack = stream.getAudioTracks()[0]
    if (audioTrack) {
      this.localStream.addTrack(audioTrack)
    }
    
    // Update all peer connections
    this.updateAudioTracks()
  }
  
  restoreVideoTrack() {
    if (!this.localStream || !this.cameraStream) return
    
    // Stop existing video tracks
    this.localStream.getVideoTracks().forEach(track => track.stop())
    
    // Add camera video track back
    const videoTrack = this.cameraStream.getVideoTracks()[0]
    if (videoTrack) {
      this.localStream.addTrack(videoTrack.clone())
    }
    
    // Update all peer connections
    this.updateVideoTracks()
    
    // Stop and clear screen stream
    if (this.screenStream) {
      this.screenStream.getTracks().forEach(track => track.stop())
      this.screenStream = null
    }
  }
  
  private updateVideoTracks() {
    if (!this.localStream) return
    
    const videoTrack = this.localStream.getVideoTracks()[0]
    if (!videoTrack) return
    
    this.peerConnections.forEach(pc => {
      const sender = pc.getSenders().find(s => s.track?.kind === 'video')
      if (sender) {
        sender.replaceTrack(videoTrack)
      }
    })
  }
  
  private updateAudioTracks() {
    if (!this.localStream) return
    
    const audioTrack = this.localStream.getAudioTracks()[0]
    if (!audioTrack) return
    
    this.peerConnections.forEach(pc => {
      const sender = pc.getSenders().find(s => s.track?.kind === 'audio')
      if (sender) {
        sender.replaceTrack(audioTrack)
      }
    })
  }
  
  async switchCamera(deviceId: string) {
    if (!this.localStream) return
    
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { deviceId: { exact: deviceId } },
        audio: this.currentAudioDeviceId ? { deviceId: { exact: this.currentAudioDeviceId } } : true
      })
      
      // Stop old tracks
      this.localStream.getTracks().forEach(track => track.stop())
      
      // Replace with new stream
      this.localStream = stream
      this.cameraStream = stream
      this.currentVideoDeviceId = deviceId
      
      // Update all peer connections
      this.updateVideoTracks()
      
      return stream
    } catch (error) {
      console.error('Error switching camera:', error)
      throw new Error('Failed to switch camera')
    }
  }

  createPeerConnection(userId: string): RTCPeerConnection {
    const pc = new RTCPeerConnection(RTC_CONFIG)

    // Add local stream tracks
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => {
        pc.addTrack(track, this.localStream!)
      })
    }

    // Handle incoming stream
    pc.ontrack = (event) => {
      const [remoteStream] = event.streams
      if (this.onStreamCallback) {
        this.onStreamCallback(userId, remoteStream)
      }
    }

    // Handle ICE candidates
    pc.onicecandidate = (event) => {
      if (event.candidate) {
        // Send ICE candidate to remote peer via signaling
        this.sendSignal(userId, {
          type: 'ice-candidate',
          candidate: event.candidate
        })
      }
    }

    // Handle connection state changes
    pc.onconnectionstatechange = () => {
      console.log(`Connection state for ${userId}:`, pc.connectionState)
      
      if (pc.connectionState === 'disconnected' || pc.connectionState === 'failed') {
        this.removePeerConnection(userId)
        if (this.onUserDisconnectedCallback) {
          this.onUserDisconnectedCallback(userId)
        }
      }
    }

    this.peerConnections.set(userId, pc)
    return pc
  }

  async createOffer(userId: string): Promise<RTCSessionDescriptionInit> {
    const pc = this.peerConnections.get(userId) || this.createPeerConnection(userId)
    
    const offer = await pc.createOffer({
      offerToReceiveAudio: true,
      offerToReceiveVideo: true
    })
    
    await pc.setLocalDescription(offer)
    return offer
  }

  async createAnswer(userId: string, offer: RTCSessionDescriptionInit): Promise<RTCSessionDescriptionInit> {
    const pc = this.peerConnections.get(userId) || this.createPeerConnection(userId)
    
    await pc.setRemoteDescription(offer)
    const answer = await pc.createAnswer()
    await pc.setLocalDescription(answer)
    
    return answer
  }

  async handleAnswer(userId: string, answer: RTCSessionDescriptionInit): Promise<void> {
    const pc = this.peerConnections.get(userId)
    if (pc) {
      await pc.setRemoteDescription(answer)
    }
  }

  async handleIceCandidate(userId: string, candidate: RTCIceCandidateInit): Promise<void> {
    const pc = this.peerConnections.get(userId)
    if (pc) {
      await pc.addIceCandidate(candidate)
    }
  }

  removePeerConnection(userId: string): void {
    const pc = this.peerConnections.get(userId)
    if (pc) {
      pc.close()
      this.peerConnections.delete(userId)
    }
  }

  replaceVideoTrack(newStream: MediaStream): void {
    const videoTrack = newStream.getVideoTracks()[0]
    
    this.peerConnections.forEach(async (pc) => {
      const sender = pc.getSenders().find(s => 
        s.track && s.track.kind === 'video'
      )
      
      if (sender && videoTrack) {
        await sender.replaceTrack(videoTrack)
      }
    })
  }

  onStream(callback: (userId: string, stream: MediaStream) => void): void {
    this.onStreamCallback = callback
  }

  onUserDisconnected(callback: (userId: string) => void): void {
    this.onUserDisconnectedCallback = callback
  }

  private sendSignal(userId: string, signal: any): void {
    // This will be implemented by the component using RTCManager
    // to send signals via Socket.IO
  }

  setSendSignalCallback(callback: (userId: string, signal: any) => void): void {
    this.sendSignal = callback
  }

  cleanup(): void {
    // Close all peer connections
    this.peerConnections.forEach(pc => pc.close())
    this.peerConnections.clear()

    // Stop local stream
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop())
      this.localStream = null
    }
  }

  getLocalStream(): MediaStream | null {
    return this.localStream
  }

  getPeerConnection(userId: string): RTCPeerConnection | undefined {
    return this.peerConnections.get(userId)
  }
}

export const rtcManager = new RTCManager()
