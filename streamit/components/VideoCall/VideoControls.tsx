'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { useVideoCallStoreContext } from '@/providers/StoreProvider'
import { rtcManager } from '@/lib/rtc'
import { SettingsModal } from './SettingsModal'
import {
  Mic,
  MicOff,
  Video,
  VideoOff,
  Monitor,
  MonitorOff,
  Settings as SettingsIcon,
  PhoneOff,
  ChevronUp,
  ChevronDown,
  Camera,
  Headphones,
  Image as ImageIcon,
  Palette,
  X
} from 'lucide-react'

interface MediaDevice {
  deviceId: string
  label: string
  kind: MediaDeviceKind
}

export function VideoControls() {
  const [isScreenSharingState, setIsScreenSharing] = useState(false)
  const [notification, setNotification] = useState<string | null>(null)
  const [showCameraDropdown, setShowCameraDropdown] = useState(false)
  const [showMicDropdown, setShowMicDropdown] = useState(false)
  const [showSettingsModal, setShowSettingsModal] = useState(false)
  const [devices, setDevices] = useState<MediaDevice[]>([])
  const [selectedCamera, setSelectedCamera] = useState('')
  const [selectedMicrophone, setSelectedMicrophone] = useState('')
  const [showBackgroundOptions, setShowBackgroundOptions] = useState(false)
  const [backgroundType, setBackgroundType] = useState<'none' | 'blur' | 'color' | 'image'>('none')
  const [backgroundColor, setBackgroundColor] = useState('#1e40af')
  const backgroundRef = useRef<HTMLDivElement>(null)

  const toggleAudio = useVideoCallStoreContext((state) => state.toggleAudio)
  const toggleVideo = useVideoCallStoreContext((state) => state.toggleVideo)
  const toggleScreenShare = useVideoCallStoreContext((state) => state.toggleScreenShare)
  const isAudioMuted = useVideoCallStoreContext((state) => state.isAudioMuted)
  const isVideoMuted = useVideoCallStoreContext((state) => state.isVideoMuted)
  const isScreenSharing = useVideoCallStoreContext((state) => state.isScreenSharing)
  const currentUser = useVideoCallStoreContext((state) => state.currentUser)

  // Load available devices
  const loadDevices = useCallback(async () => {
    try {
      // First try to get permissions with both audio and video
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: true, 
        video: { 
          width: { ideal: 1280 },
          height: { ideal: 720 },
          frameRate: { ideal: 30 }
        }
      }).catch(async (videoError) => {
        console.warn('Could not get both audio and video, trying audio only')
        // If that fails, try with just audio
        return navigator.mediaDevices.getUserMedia({ audio: true, video: false })
      }).catch(async (audioError) => {
        console.warn('Could not get audio, trying video only')
        // If that fails too, try with just video
        return navigator.mediaDevices.getUserMedia({ 
          video: { 
            width: { ideal: 1280 },
            height: { ideal: 720 },
            frameRate: { ideal: 30 }
          },
          audio: false 
        })
      })
      
      // Get the device list
      const deviceList = await navigator.mediaDevices.enumerateDevices()
      
      // Stop all tracks to release devices
      stream.getTracks().forEach(track => track.stop())
      
      const mediaDevices: MediaDevice[] = deviceList
        .filter(device => device.kind === 'audioinput' || device.kind === 'videoinput')
        .map(device => ({
          deviceId: device.deviceId,
          label: device.label || `${device.kind.split('input')[0]} ${device.deviceId.slice(0, 4)}`,
          kind: device.kind as MediaDeviceKind
        }))
      
      setDevices(mediaDevices)

      // Set default devices if not already set
      const videoDevices = mediaDevices.filter(d => d.kind === 'videoinput')
      const audioDevices = mediaDevices.filter(d => d.kind === 'audioinput')
      
      if (videoDevices.length > 0 && !selectedCamera) {
        setSelectedCamera(videoDevices[0].deviceId)
      }
      
      if (audioDevices.length > 0 && !selectedMicrophone) {
        setSelectedMicrophone(audioDevices[0].deviceId)
      }
    } catch (error) {
      console.error('Error loading devices:', error)
    }
  }, [selectedCamera, selectedMicrophone])

  useEffect(() => {
    loadDevices()
    
    // Add event listener for device changes
    navigator.mediaDevices.addEventListener('devicechange', loadDevices)
    
    return () => {
      navigator.mediaDevices.removeEventListener('devicechange', loadDevices)
    }
  }, [loadDevices])

  const showNotification = (message: string) => {
    setNotification(message)
    setTimeout(() => setNotification(null), 2000)
  }

  const handleToggleAudio = () => {
    toggleAudio()
    showNotification(isAudioMuted ? 'Microphone unmuted' : 'Microphone muted')
  }

  const handleToggleVideo = async () => {
    try {
      if (isVideoMuted && selectedCamera) {
        // When turning video back on, reinitialize with selected camera
        await rtcManager.switchCamera(selectedCamera)
      }
      toggleVideo()
      showNotification(isVideoMuted ? 'Camera turned on' : 'Camera turned off')
    } catch (error) {
      console.error('Error toggling video:', error)
      showNotification('Failed to toggle video')
    }
  }

  const handleToggleScreenShare = useCallback(async () => {
    try {
      if (!isScreenSharing) {
        // Start screen sharing
        const screenStream = await rtcManager.getDisplayMedia()

        // Replace video track with screen share
        rtcManager.replaceVideoTrack(screenStream)

        // Listen for screen share end
        screenStream.getVideoTracks()[0].onended = () => {
          handleStopScreenShare()
        }

        toggleScreenShare()
        showNotification('Screen sharing started')
      } else {
        handleStopScreenShare()
      }
    } catch (error) {
      console.error('Error toggling screen share:', error)
      showNotification('Failed to toggle screen share')
    }
  }, [isScreenSharing, toggleScreenShare])

  const handleStopScreenShare = useCallback(async () => {
    try {
      // Switch back to camera
      rtcManager.restoreVideoTrack()
      toggleScreenShare()
      showNotification('Screen sharing stopped')
    } catch (error) {
      console.error('Error stopping screen share:', error)
      showNotification('Failed to stop screen share')
    }
  }, [toggleScreenShare])

  const handleCameraSelect = useCallback(async (deviceId: string) => {
    if (deviceId === selectedCamera) {
      setShowCameraDropdown(false)
      return
    }
    
    try {
      await rtcManager.switchDevice('videoinput', deviceId)
      setSelectedCamera(deviceId)
      setShowCameraDropdown(false)
      showNotification('Camera changed')
    } catch (error) {
      console.error('Error switching camera:', error)
      showNotification('Failed to switch camera')
    }
  }, [selectedCamera])

  const handleMicrophoneSelect = useCallback(async (deviceId: string) => {
    if (deviceId === selectedMicrophone) {
      setShowMicDropdown(false)
      return
    }
    
    try {
      await rtcManager.switchDevice('audioinput', deviceId)
      setSelectedMicrophone(deviceId)
      setShowMicDropdown(false)
      showNotification('Microphone changed')
    } catch (error) {
      console.error('Error switching microphone:', error)
      showNotification('Failed to switch microphone')
    }
  }, [selectedMicrophone])

  const cameras = devices.filter((d: MediaDevice) => d.kind === 'videoinput')
  const microphones = devices.filter((d: MediaDevice) => d.kind === 'audioinput')

  // Apply background effect
  useEffect(() => {
    if (!backgroundRef.current) return
    
    const videoElement = backgroundRef.current.querySelector('video')
    if (!videoElement) return
    
    // Reset previous effects
    videoElement.style.filter = 'none'
    videoElement.style.backgroundColor = 'transparent'
    
    switch (backgroundType) {
      case 'blur':
        videoElement.style.filter = 'blur(10px)'
        break
      case 'color':
        videoElement.style.backgroundColor = backgroundColor
        break
      case 'image':
        // For image background, you would typically use a canvas or WebGL solution
        // This is a simplified version
        videoElement.style.backgroundImage = `url('/background.jpg')`
        videoElement.style.backgroundSize = 'cover'
        videoElement.style.backgroundPosition = 'center'
        break
      default:
        break
    }
  }, [backgroundType, backgroundColor])

  return (
    <div className="p-4">
      <div className="video-controls" ref={backgroundRef}>
        {/* Audio controls with dropdown */}
        <div className="relative">
          <div className="flex">
            <button
              onClick={handleToggleAudio}
              className={`control-btn ${isAudioMuted ? 'active' : 'inactive'} rounded-r-none`}
              title={isAudioMuted ? 'Unmute microphone' : 'Mute microphone'}
            >
              {isAudioMuted ? (
                <MicOff className="h-6 w-6" />
              ) : (
                <Mic className="h-6 w-6" />
              )}
            </button>
            {microphones.length > 1 && (
              <button
                onClick={() => setShowMicDropdown(!showMicDropdown)}
                className="control-btn inactive rounded-l-none border-l border-white/20 w-8"
                title="Select microphone"
              >
                <ChevronUp className="h-4 w-4" />
              </button>
            )}
          </div>

          {showMicDropdown && microphones.length > 1 && (
            <div className="absolute bottom-full left-0 mb-2 glass-dark rounded-lg border border-white/20 min-w-48 z-10">
              <div className="p-2">
                <div className="text-white text-xs font-medium mb-2 flex items-center gap-2">
                  <Headphones className="h-3 w-3" />
                  Select Microphone
                </div>
                {microphones.map((mic: MediaDevice) => (
                  <div 
                    key={mic.deviceId}
                    className="px-4 py-2 text-white hover:bg-white/10 cursor-pointer flex items-center"
                    onClick={() => handleMicrophoneSelect(mic.deviceId)}
                  >
                    <span className={selectedMicrophone === mic.deviceId ? 'font-semibold' : ''}>
                      {mic.label}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Video controls with dropdown */}
        <div className="relative">
          <div className="flex">
            <button
              onClick={handleToggleVideo}
              className={`control-btn ${isVideoMuted ? 'active' : 'inactive'} rounded-r-none`}
              title={isVideoMuted ? 'Turn on camera' : 'Turn off camera'}
            >
              {isVideoMuted ? (
                <VideoOff className="h-6 w-6" />
              ) : (
                <Video className="h-6 w-6" />
              )}
            </button>
            {cameras.length > 1 && (
              <button
                onClick={() => setShowCameraDropdown(!showCameraDropdown)}
                className="control-btn inactive rounded-l-none border-l border-white/20 w-8"
                title="Select camera"
              >
                <ChevronUp className="h-4 w-4" />
              </button>
            )}
          </div>

          {showCameraDropdown && cameras.length > 1 && (
            <div className="absolute bottom-full left-0 mb-2 glass-dark rounded-lg border border-white/20 min-w-48 z-10">
              <div className="p-2">
                <div className="text-white text-xs font-medium mb-2 flex items-center gap-2">
                  <Camera className="h-3 w-3" />
                  Select Camera
                </div>
                {cameras.map((camera: MediaDevice) => (
                  <div 
                    key={camera.deviceId}
                    className="px-4 py-2 text-white hover:bg-white/10 cursor-pointer flex items-center"
                    onClick={() => handleCameraSelect(camera.deviceId)}
                  >
                    <span className={selectedCamera === camera.deviceId ? 'font-semibold' : ''}>
                      {camera.label}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Screen share toggle */}
        <button
          onClick={handleToggleScreenShare}
          className={`control-btn ${isScreenSharing ? 'active' : 'inactive'}`}
          title={isScreenSharing ? 'Stop screen sharing' : 'Share screen'}
        >
          {isScreenSharing ? (
            <MonitorOff className="h-6 w-6" />
          ) : (
            <Monitor className="h-6 w-6" />
          )}
        </button>

        {/* Background options */}
        <div className="relative">
          <button
            onClick={() => setShowBackgroundOptions(!showBackgroundOptions)}
            className="control-btn inactive"
            title="Background options"
          >
            <Palette className="h-6 w-6" />
          </button>
          
          {showBackgroundOptions && (
            <div className="absolute bottom-full left-0 mb-2 glass-dark rounded-lg border border-white/20 p-3 min-w-48 z-10">
              <div className="flex justify-between items-center mb-2">
                <h4 className="text-white text-sm font-medium">Background</h4>
                <button 
                  onClick={() => setShowBackgroundOptions(false)}
                  className="text-white/70 hover:text-white"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
              
              <div className="grid grid-cols-2 gap-2 mb-3">
                <button
                  onClick={() => setBackgroundType('none')}
                  className={`p-2 rounded ${backgroundType === 'none' ? 'bg-blue-600' : 'bg-white/10'} text-white text-sm`}
                >
                  None
                </button>
                <button
                  onClick={() => setBackgroundType('blur')}
                  className={`p-2 rounded ${backgroundType === 'blur' ? 'bg-blue-600' : 'bg-white/10'} text-white text-sm`}
                >
                  Blur
                </button>
                <button
                  onClick={() => setBackgroundType('color')}
                  className={`p-2 rounded ${backgroundType === 'color' ? 'bg-blue-600' : 'bg-white/10'} text-white text-sm`}
                >
                  Color
                </button>
                <button
                  onClick={() => setBackgroundType('image')}
                  className={`p-2 rounded ${backgroundType === 'image' ? 'bg-blue-600' : 'bg-white/10'} text-white text-sm`}
                >
                  Image
                </button>
              </div>
              
              {backgroundType === 'color' && (
                <div className="mt-2">
                  <label className="block text-white/80 text-xs mb-1">Color:</label>
                  <input
                    type="color"
                    value={backgroundColor}
                    onChange={(e) => setBackgroundColor(e.target.value)}
                    className="w-full h-8 rounded cursor-pointer"
                  />
                </div>
              )}
              
              {backgroundType === 'image' && (
                <div className="mt-2 text-center">
                  <button className="bg-blue-600 hover:bg-blue-700 text-white text-sm px-3 py-1.5 rounded-md flex items-center gap-2 mx-auto">
                    <ImageIcon className="h-4 w-4" />
                    Upload Image
                  </button>
                  <p className="text-xs text-white/60 mt-2">
                    For demo, using sample background
                  </p>
                </div>
              )}
            </div>
          )}
        </div>


      </div>

      {/* Settings Modal */}
      <SettingsModal
        isOpen={showSettingsModal}
        onClose={() => setShowSettingsModal(false)}
      />

      {/* Notification */}
      {notification && (
        <div className="fixed bottom-20 left-1/2 transform -translate-x-1/2 glass-dark px-4 py-2 rounded-lg text-white z-50 fade-in">
          {notification}
        </div>
      )}
    </div>
  )
}
