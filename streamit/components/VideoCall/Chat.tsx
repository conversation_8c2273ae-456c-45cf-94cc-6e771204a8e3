'use client'

import { useState, useRef, useEffect } from 'react'
import { useVideoCallStoreContext } from '@/providers/StoreProvider'
import { socketManager } from '@/lib/socket'
import { Send, X, MessageCircle } from 'lucide-react'

export function Chat() {
  const [message, setMessage] = useState('')
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const messages = useVideoCallStoreContext((state) => state.messages)
  const currentUser = useVideoCallStoreContext((state) => state.currentUser)
  const toggleChat = useVideoCallStoreContext((state) => state.toggleChat)
  const clearUnreadCount = useVideoCallStoreContext((state) => state.clearUnreadCount)
  const securitySettings = useVideoCallStoreContext((state) => state.securitySettings)
  const detectSpam = useVideoCallStoreContext((state) => state.detectSpam)
  const blockUser = useVideoCallStoreContext((state) => state.blockUser)

  const isAdmin = currentUser?.role === 'host' || currentUser?.role === 'co-host'

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Clear unread count when chat is opened
  useEffect(() => {
    clearUnreadCount()
  }, [clearUnreadCount])

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault()

    if (!message.trim() || !currentUser) return

    // Check for spam
    if (securitySettings.antiSpamEnabled && detectSpam(currentUser.id)) {
      alert('You are sending messages too quickly. Please slow down.')
      return
    }

    // Send message via Socket.IO
    socketManager.sendChatMessage(message.trim(), currentUser.name)

    setMessage('')
  }

  const handleBlockUser = (userId: string) => {
    if (isAdmin) {
      blockUser(userId)
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  return (
    <div className="chat-container flex flex-col h-full">
      {/* Chat header */}
      <div className="flex items-center justify-between p-3 border-b border-white/10">
        <h3 className="text-white font-semibold text-sm flex items-center gap-2">
          <MessageCircle className="h-4 w-4" />
          Chat
        </h3>
        <div className="flex items-center gap-2">
          {messages.length > 0 && (
            <span className="text-white/60 text-xs">
              {messages.length} messages
            </span>
          )}
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-3 space-y-3">
        {messages.length === 0 ? (
          <div className="text-center text-white/60 py-8">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-3">
              <Send className="h-6 w-6 text-white" />
            </div>
            <p className="text-sm font-medium mb-1">No messages yet</p>
            <p className="text-xs">Start the conversation!</p>
          </div>
        ) : (
          messages
            .filter(msg => !msg.isBlocked) // Filter out blocked messages
            .map((msg) => (
            <div
              key={msg.id}
              className={`flex flex-col ${
                msg.userId === currentUser?.id ? 'items-end' : 'items-start'
              } ${msg.isSpam ? 'opacity-50' : ''}`}
            >
              <div
                className={`chat-message max-w-[90%] p-2 relative ${
                  msg.userId === currentUser?.id
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500'
                    : msg.isSpam
                      ? 'bg-red-500/20 border border-red-500/50'
                      : 'bg-white/10'
                }`}
              >
                <p className="text-white text-xs leading-relaxed">{msg.message}</p>
                {msg.isSpam && (
                  <div className="absolute top-0 right-0 bg-red-500 text-white text-xs px-1 rounded-bl">
                    SPAM
                  </div>
                )}
              </div>
              <div className="flex items-center space-x-1 mt-1 text-xs text-white/50">
                <span className="font-medium text-xs">{msg.userName}</span>
                <span>•</span>
                <span className="text-xs">{formatTime(msg.timestamp)}</span>
                {isAdmin && msg.userId !== currentUser?.id && (
                  <button
                    onClick={() => handleBlockUser(msg.userId)}
                    className="text-red-400 hover:text-red-300 text-xs ml-2"
                  >
                    Block
                  </button>
                )}
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Message input */}
      <div className="p-3 border-t border-white/10">
        <form onSubmit={handleSendMessage} className="flex space-x-2">
          <input
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Type a message..."
            className="glass-input flex-1 text-sm py-2"
            maxLength={500}
            autoComplete="off"
          />
          <button
            type="submit"
            disabled={!message.trim()}
            className={`p-2 rounded-lg transition-all ${
              message.trim()
                ? 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white'
                : 'bg-white/10 text-white/50 cursor-not-allowed'
            }`}
          >
            <Send className="h-4 w-4" />
          </button>
        </form>
      </div>
    </div>
  )
}
