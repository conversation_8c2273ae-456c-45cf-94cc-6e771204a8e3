'use client'

import { useEffect, useState, useCallback } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { VideoCallRoom } from '@/components/VideoCall/VideoCallRoom'
import { useVideoCallStoreContext } from '@/providers/StoreProvider'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Loader2 } from 'lucide-react'

export default function RoomPage() {
  const params = useParams()
  const router = useRouter()
  const roomId = params.id as string
  const [userName, setUserName] = useState('')
  const [isJoining, setIsJoining] = useState(false)
  const [hasJoined, setHasJoined] = useState(false)
  const [devices, setDevices] = useState<MediaDeviceInfo[]>([])
  const [videoDevices, setVideoDevices] = useState<MediaDeviceInfo[]>([])
  const [audioInputDevices, setAudioInputDevices] = useState<MediaDeviceInfo[]>([])
  const [selectedVideoDevice, setSelectedVideoDevice] = useState('')
  const [selectedAudioInputDevice, setSelectedAudioInputDevice] = useState('')
  
  const setRoomId = useVideoCallStoreContext((state) => state.setRoomId)
  const setCurrentUser = useVideoCallStoreContext((state) => state.setCurrentUser)

  // Load available devices
  const loadDevices = useCallback(async () => {
    try {
      // Request permissions and get devices
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: true, 
        video: { width: 1280, height: 720 } 
      })
      
      // Stop all tracks to release devices
      stream.getTracks().forEach(track => track.stop())
      
      // Get the list of devices
      const deviceList = await navigator.mediaDevices.enumerateDevices()
      
      // Filter and set devices
      const videoInputs = deviceList.filter(device => device.kind === 'videoinput')
      const audioInputs = deviceList.filter(device => device.kind === 'audioinput')
      
      setVideoDevices(videoInputs)
      setAudioInputDevices(audioInputs)
      
      // Set default devices if available
      if (videoInputs.length > 0) {
        setSelectedVideoDevice(videoInputs[0].deviceId)
      }
      
      if (audioInputs.length > 0) {
        setSelectedAudioInputDevice(audioInputs[0].deviceId)
      }
    } catch (error) {
      console.error('Error loading devices:', error)
    }
  }, [])

  // Load devices on component mount
  useEffect(() => {
    const init = async () => {
      await loadDevices()
      
      // Check if user name is already stored
      const storedName = localStorage.getItem('userName')
      if (storedName) {
        setUserName(storedName)
      }
    }
    
    init()
  }, [])

  const handleJoinRoom = async (name: string) => {
    if (!name.trim()) {
      alert('Please enter your name')
      return
    }

    setIsJoining(true)
    
    try {
      // Set room ID in store
      setRoomId(roomId)
      
      // Initialize media with selected devices
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: selectedVideoDevice ? { 
            deviceId: { exact: selectedVideoDevice },
            width: { ideal: 1280 },
            height: { ideal: 720 }
          } : false,
          audio: selectedAudioInputDevice ? { 
            deviceId: { exact: selectedAudioInputDevice },
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          } : false
        })
        
        // Store the stream in the RTC manager
        const rtcManager = (await import('@/lib/rtc')).rtcManager
        await rtcManager.getUserMedia()
      } catch (error) {
        console.error('Error initializing media:', error)
      }
      
      // Create user object
      const user = {
        id: Math.random().toString(36).substring(2, 15),
        name: name.trim(),
        isAudioMuted: false,
        isVideoMuted: false,
        isScreenSharing: false,
        role: 'host' as const, // First user is always host
        isHandRaised: false,
        joinedAt: new Date(),
        lastActivity: new Date()
      }
      
      // Set current user in store
      setCurrentUser(user)
      
      // Store user name for future sessions
      localStorage.setItem('userName', name.trim())
      
      setHasJoined(true)
    } catch (error) {
      console.error('Error joining room:', error)
      alert('Failed to join room. Please try again.')
    } finally {
      setIsJoining(false)
    }
  }

  const handleNameSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleJoinRoom(userName)
  }

  if (hasJoined) {
    return <VideoCallRoom roomId={roomId} />
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-purple-950">
      <div className="relative min-h-screen">
        <div className="animated-bg"></div>
        <div className="min-h-screen flex items-center justify-center p-4 relative z-10">
          <div className="glass p-8 max-w-md w-full rounded-xl">
            <Card className="w-full border-0 bg-transparent shadow-none">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl font-bold text-white mb-2">Join Meeting</CardTitle>
                <CardDescription className="text-purple-200">
                  Meeting ID: <span className="font-mono font-semibold">{roomId}</span>
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-white">Your Name</Label>
                    <Input
                      id="name"
                      placeholder="Enter your name"
                      value={userName}
                      onChange={(e) => setUserName(e.target.value)}
                      className="w-full bg-white/10 border-white/20 text-white placeholder:text-white/50"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label className="text-white">Camera</Label>
                    <Select 
                      value={selectedVideoDevice} 
                      onValueChange={setSelectedVideoDevice}
                      disabled={videoDevices.length === 0}
                    >
                      <SelectTrigger className="bg-white/10 border-white/20 text-white">
                        <SelectValue placeholder={videoDevices.length > 0 ? 'Select camera' : 'No cameras found'} />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-800 border-gray-700 text-white">
                        {videoDevices.map((device) => (
                          <SelectItem 
                            key={device.deviceId} 
                            value={device.deviceId}
                            className="hover:bg-gray-700 focus:bg-gray-700"
                          >
                            {device.label || `Camera ${device.deviceId.substring(0, 4)}`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label className="text-white">Microphone</Label>
                    <Select 
                      value={selectedAudioInputDevice} 
                      onValueChange={setSelectedAudioInputDevice}
                      disabled={audioInputDevices.length === 0}
                    >
                      <SelectTrigger className="bg-white/10 border-white/20 text-white">
                        <SelectValue placeholder={audioInputDevices.length > 0 ? 'Select microphone' : 'No microphones found'} />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-800 border-gray-700 text-white">
                        {audioInputDevices.map((device) => (
                          <SelectItem 
                            key={device.deviceId} 
                            value={device.deviceId}
                            className="hover:bg-gray-700 focus:bg-gray-700"
                          >
                            {device.label || `Mic ${device.deviceId.substring(0, 4)}`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="flex gap-3 pt-2">
                  <Button 
                    onClick={() => router.push('/')}
                    variant="outline"
                    className="flex-1 bg-transparent text-white border-white/20 hover:bg-white/10 hover:text-white"
                  >
                    Cancel
                  </Button>
                  <Button 
                    onClick={() => handleJoinRoom(userName)}
                    className="flex-1 bg-purple-600 hover:bg-purple-700 text-white"
                    disabled={isJoining || !userName.trim()}
                  >
                    {isJoining ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Joining...
                      </>
                    ) : 'Join Room'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
