{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./server", "module": "NodeNext", "moduleResolution": "NodeNext", "target": "ES2020", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "include": ["server/**/*"], "exclude": ["node_modules", "dist", "app", "components", "lib"]}