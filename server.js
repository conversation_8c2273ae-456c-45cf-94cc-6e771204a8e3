const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"]
    }
});

// Get port from environment variable or default to 3000
const PORT = process.env.PORT || 3000;

// Store active meetings and participants
const meetings = new Map();
const participants = new Map();

// Serve static files from current directory
app.use(express.static(__dirname));

// Route for root - serve streamit-pro.html
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'streamit-pro.html'));
});

// Route for index.html - also serve streamit-pro.html
app.get('/index.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'streamit-pro.html'));
});

// Route for streamit-pro.html
app.get('/streamit-pro.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'streamit-pro.html'));
});

// Catch all other routes and serve streamit-pro.html (for SPA routing)
app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'streamit-pro.html'));
});

// Socket.IO connection handling
io.on('connection', (socket) => {
    console.log(`🔌 User connected: ${socket.id}`);

    // Join meeting
    socket.on('join-meeting', (data) => {
        const { meetingId, userName } = data;
        console.log(`👤 ${userName} joining meeting ${meetingId}`);

        // Add participant to meeting
        if (!meetings.has(meetingId)) {
            meetings.set(meetingId, new Set());
        }

        meetings.get(meetingId).add(socket.id);
        participants.set(socket.id, {
            id: socket.id,
            name: userName,
            meetingId,
            isMuted: false,
            isVideoOff: false
        });

        // Join socket room
        socket.join(meetingId);

        // Get existing participants
        const existingParticipants = Array.from(meetings.get(meetingId))
            .filter(id => id !== socket.id)
            .map(id => participants.get(id))
            .filter(p => p);

        // Send existing participants to new user
        socket.emit('existing-participants', existingParticipants);

        // Notify others about new participant
        socket.to(meetingId).emit('participant-joined', participants.get(socket.id));

        console.log(`📊 Meeting ${meetingId} now has ${meetings.get(meetingId).size} participants`);
    });

    // WebRTC signaling
    socket.on('offer', (data) => {
        socket.to(data.target).emit('offer', {
            offer: data.offer,
            sender: socket.id
        });
    });

    socket.on('answer', (data) => {
        socket.to(data.target).emit('answer', {
            answer: data.answer,
            sender: socket.id
        });
    });

    socket.on('ice-candidate', (data) => {
        socket.to(data.target).emit('ice-candidate', {
            candidate: data.candidate,
            sender: socket.id
        });
    });

    // Chat messages
    socket.on('chat-message', (data) => {
        const participant = participants.get(socket.id);
        if (participant) {
            const message = {
                id: Date.now(),
                sender: participant.name,
                content: data.message,
                timestamp: new Date(),
                isSystem: false
            };
            io.to(participant.meetingId).emit('chat-message', message);
        }
    });

    // Participant state updates
    socket.on('toggle-audio', (data) => {
        const participant = participants.get(socket.id);
        if (participant) {
            participant.isMuted = data.isMuted;
            socket.to(participant.meetingId).emit('participant-audio-toggle', {
                participantId: socket.id,
                isMuted: data.isMuted
            });
        }
    });

    socket.on('toggle-video', (data) => {
        const participant = participants.get(socket.id);
        if (participant) {
            participant.isVideoOff = data.isVideoOff;
            socket.to(participant.meetingId).emit('participant-video-toggle', {
                participantId: socket.id,
                isVideoOff: data.isVideoOff
            });
        }
    });

    // Handle disconnect
    socket.on('disconnect', () => {
        console.log(`🔌 User disconnected: ${socket.id}`);

        const participant = participants.get(socket.id);
        if (participant) {
            const meetingId = participant.meetingId;

            // Remove from meeting
            if (meetings.has(meetingId)) {
                meetings.get(meetingId).delete(socket.id);
                if (meetings.get(meetingId).size === 0) {
                    meetings.delete(meetingId);
                    console.log(`🗑️ Meeting ${meetingId} deleted (no participants)`);
                }
            }

            // Notify others about participant leaving
            socket.to(meetingId).emit('participant-left', socket.id);

            // Remove participant
            participants.delete(socket.id);

            console.log(`👋 ${participant.name} left meeting ${meetingId}`);
        }
    });
});

// Start server
server.listen(PORT, () => {
    console.log(`🚀 StreamIt Pro server running on port ${PORT}`);
    console.log(`🌐 Access your app at: http://localhost:${PORT}`);
    console.log(`📱 For Heroku: https://your-app-name.herokuapp.com`);
    console.log(`🔌 Socket.IO enabled for real-time communication`);
});

module.exports = app;
