'use client'

import { useState, useEffect } from 'react'
import { useVideoCallStore } from '@/lib/store'
import { rtcManager } from '@/lib/rtc'
import { 
  X, 
  Camera, 
  Mic, 
  Monitor, 
  Volume2, 
  Settings, 
  Shield,

  Image,
  ChevronDown,
  Check
} from 'lucide-react'

interface MediaDevice {
  deviceId: string
  label: string
  kind: MediaDeviceKind
}

interface SettingsModalProps {
  isOpen: boolean
  onClose: () => void
}

export function SettingsModal({ isOpen, onClose }: SettingsModalProps) {
  const [activeTab, setActiveTab] = useState('video')
  const [devices, setDevices] = useState<MediaDevice[]>([])
  const [selectedCamera, setSelectedCamera] = useState('')
  const [selectedMicrophone, setSelectedMicrophone] = useState('')
  const [selectedSpeaker, setSelectedSpeaker] = useState('')
  const [backgroundBlur, setBackgroundBlur] = useState(false)
  const [selectedBackground, setSelectedBackground] = useState('none')
  const [videoQuality, setVideoQuality] = useState('hd')
  const [audioQuality, setAudioQuality] = useState('high')
  const [noiseSuppression, setNoiseSuppression] = useState(true)
  const [echoCancellation, setEchoCancellation] = useState(true)
  const [autoGainControl, setAutoGainControl] = useState(true)
  const [encryptionEnabled, setEncryptionEnabled] = useState(true)
  const [antiSpamEnabled, setAntiSpamEnabled] = useState(true)
  const [showCameraDropdown, setShowCameraDropdown] = useState(false)
  const [showMicDropdown, setShowMicDropdown] = useState(false)

  const { currentUser } = useVideoCallStore()

  // Background options
  const backgroundOptions = [
    { id: 'none', name: 'None', preview: null },
    { id: 'blur', name: 'Blur Background', preview: null },
    { id: 'office', name: 'Modern Office', preview: '/backgrounds/office.jpg' },
    { id: 'nature', name: 'Nature Scene', preview: '/backgrounds/nature.jpg' },
    { id: 'abstract', name: 'Abstract Blue', preview: '/backgrounds/abstract.jpg' },
    { id: 'gradient', name: 'Purple Gradient', preview: '/backgrounds/gradient.jpg' }
  ]

  // Video quality options
  const videoQualityOptions = [
    { id: 'sd', name: 'SD (480p)', resolution: '640x480' },
    { id: 'hd', name: 'HD (720p)', resolution: '1280x720' },
    { id: 'fhd', name: 'Full HD (1080p)', resolution: '1920x1080' },
    { id: '4k', name: '4K (2160p)', resolution: '3840x2160' }
  ]

  // Load available devices
  useEffect(() => {
    const loadDevices = async () => {
      try {
        const deviceList = await navigator.mediaDevices.enumerateDevices()
        const mediaDevices: MediaDevice[] = deviceList.map(device => ({
          deviceId: device.deviceId,
          label: device.label || `${device.kind} ${device.deviceId.slice(0, 8)}`,
          kind: device.kind
        }))
        setDevices(mediaDevices)

        // Set default devices
        const defaultCamera = mediaDevices.find(d => d.kind === 'videoinput')
        const defaultMic = mediaDevices.find(d => d.kind === 'audioinput')
        const defaultSpeaker = mediaDevices.find(d => d.kind === 'audiooutput')

        if (defaultCamera) setSelectedCamera(defaultCamera.deviceId)
        if (defaultMic) setSelectedMicrophone(defaultMic.deviceId)
        if (defaultSpeaker) setSelectedSpeaker(defaultSpeaker.deviceId)
      } catch (error) {
        console.error('Error loading devices:', error)
      }
    }

    if (isOpen) {
      loadDevices()
    }
  }, [isOpen])

  const handleCameraChange = async (deviceId: string) => {
    try {
      setSelectedCamera(deviceId)
      const constraints = {
        video: { 
          deviceId: { exact: deviceId },
          width: { ideal: 1280 },
          height: { ideal: 720 }
        },
        audio: false
      }
      const stream = await navigator.mediaDevices.getUserMedia(constraints)
      rtcManager.replaceVideoTrack(stream)
      setShowCameraDropdown(false)
    } catch (error) {
      console.error('Error changing camera:', error)
    }
  }

  const handleMicrophoneChange = async (deviceId: string) => {
    try {
      setSelectedMicrophone(deviceId)
      const constraints = {
        video: false,
        audio: { 
          deviceId: { exact: deviceId },
          echoCancellation,
          noiseSuppression,
          autoGainControl
        }
      }
      const stream = await navigator.mediaDevices.getUserMedia(constraints)
      // Replace audio track logic would go here
      setShowMicDropdown(false)
    } catch (error) {
      console.error('Error changing microphone:', error)
    }
  }

  const applyBackgroundEffect = (backgroundId: string) => {
    setSelectedBackground(backgroundId)
    // Background effect logic would be implemented here
    // This would typically involve canvas manipulation or WebGL shaders
  }

  const applyVideoQuality = (quality: string) => {
    setVideoQuality(quality)
    // Video quality change logic would go here
  }

  if (!isOpen) return null

  const cameras = devices.filter(d => d.kind === 'videoinput')
  const microphones = devices.filter(d => d.kind === 'audioinput')
  const speakers = devices.filter(d => d.kind === 'audiooutput')

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <div className="glass max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/10">
          <h2 className="text-2xl font-bold text-white flex items-center gap-3">
            <Settings className="h-6 w-6" />
            Meeting Settings
          </h2>
          <button
            onClick={onClose}
            className="glass-button p-2 text-white hover:text-purple-300"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="flex h-[600px]">
          {/* Sidebar */}
          <div className="w-64 p-4 border-r border-white/10">
            <div className="space-y-2">
              {[
                { id: 'video', name: 'Video', icon: Camera },
                { id: 'audio', name: 'Audio', icon: Mic },
                { id: 'background', name: 'Background', icon: Image },
                { id: 'security', name: 'Security', icon: Shield },
                { id: 'general', name: 'General', icon: Settings }
              ].map(tab => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center gap-3 p-3 rounded-lg transition-all ${
                    activeTab === tab.id 
                      ? 'bg-purple-500/30 text-white' 
                      : 'text-white/70 hover:bg-white/10 hover:text-white'
                  }`}
                >
                  <tab.icon className="h-5 w-5" />
                  {tab.name}
                </button>
              ))}
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 p-6 overflow-y-auto">
            {activeTab === 'video' && (
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-white mb-4">Video Settings</h3>
                
                {/* Camera Selection */}
                <div>
                  <label className="block text-white text-sm font-medium mb-2">Camera</label>
                  <div className="relative">
                    <button
                      onClick={() => setShowCameraDropdown(!showCameraDropdown)}
                      className="glass-input w-full flex items-center justify-between"
                    >
                      <span>{cameras.find(c => c.deviceId === selectedCamera)?.label || 'Select Camera'}</span>
                      <ChevronDown className="h-4 w-4" />
                    </button>
                    {showCameraDropdown && (
                      <div className="absolute top-full left-0 right-0 mt-1 glass-dark rounded-lg border border-white/20 z-10">
                        {cameras.map(camera => (
                          <button
                            key={camera.deviceId}
                            onClick={() => handleCameraChange(camera.deviceId)}
                            className="w-full p-3 text-left text-white hover:bg-white/10 flex items-center justify-between"
                          >
                            {camera.label}
                            {selectedCamera === camera.deviceId && <Check className="h-4 w-4" />}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Video Quality */}
                <div>
                  <label className="block text-white text-sm font-medium mb-2">Video Quality</label>
                  <div className="grid grid-cols-2 gap-2">
                    {videoQualityOptions.map(option => (
                      <button
                        key={option.id}
                        onClick={() => applyVideoQuality(option.id)}
                        className={`p-3 rounded-lg border transition-all ${
                          videoQuality === option.id
                            ? 'bg-purple-500/30 border-purple-500 text-white'
                            : 'bg-white/10 border-white/20 text-white/70 hover:bg-white/20'
                        }`}
                      >
                        <div className="font-medium">{option.name}</div>
                        <div className="text-xs opacity-70">{option.resolution}</div>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'audio' && (
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-white mb-4">Audio Settings</h3>
                
                {/* Microphone Selection */}
                <div>
                  <label className="block text-white text-sm font-medium mb-2">Microphone</label>
                  <div className="relative">
                    <button
                      onClick={() => setShowMicDropdown(!showMicDropdown)}
                      className="glass-input w-full flex items-center justify-between"
                    >
                      <span>{microphones.find(m => m.deviceId === selectedMicrophone)?.label || 'Select Microphone'}</span>
                      <ChevronDown className="h-4 w-4" />
                    </button>
                    {showMicDropdown && (
                      <div className="absolute top-full left-0 right-0 mt-1 glass-dark rounded-lg border border-white/20 z-10">
                        {microphones.map(mic => (
                          <button
                            key={mic.deviceId}
                            onClick={() => handleMicrophoneChange(mic.deviceId)}
                            className="w-full p-3 text-left text-white hover:bg-white/10 flex items-center justify-between"
                          >
                            {mic.label}
                            {selectedMicrophone === mic.deviceId && <Check className="h-4 w-4" />}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Audio Enhancement */}
                <div className="space-y-4">
                  <h4 className="text-white font-medium">Audio Enhancement</h4>
                  
                  <label className="flex items-center justify-between">
                    <span className="text-white">Noise Suppression</span>
                    <input
                      type="checkbox"
                      checked={noiseSuppression}
                      onChange={(e) => setNoiseSuppression(e.target.checked)}
                      className="w-5 h-5 rounded bg-white/10 border-white/20"
                    />
                  </label>
                  
                  <label className="flex items-center justify-between">
                    <span className="text-white">Echo Cancellation</span>
                    <input
                      type="checkbox"
                      checked={echoCancellation}
                      onChange={(e) => setEchoCancellation(e.target.checked)}
                      className="w-5 h-5 rounded bg-white/10 border-white/20"
                    />
                  </label>
                  
                  <label className="flex items-center justify-between">
                    <span className="text-white">Auto Gain Control</span>
                    <input
                      type="checkbox"
                      checked={autoGainControl}
                      onChange={(e) => setAutoGainControl(e.target.checked)}
                      className="w-5 h-5 rounded bg-white/10 border-white/20"
                    />
                  </label>
                </div>
              </div>
            )}

            {activeTab === 'background' && (
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-white mb-4">Background Effects</h3>
                
                <div className="grid grid-cols-3 gap-4">
                  {backgroundOptions.map(bg => (
                    <button
                      key={bg.id}
                      onClick={() => applyBackgroundEffect(bg.id)}
                      className={`p-4 rounded-lg border-2 transition-all ${
                        selectedBackground === bg.id
                          ? 'border-purple-500 bg-purple-500/20'
                          : 'border-white/20 bg-white/10 hover:border-white/40'
                      }`}
                    >
                      <div className="w-full h-20 bg-gradient-to-br from-gray-600 to-gray-800 rounded-lg mb-2 flex items-center justify-center">
                        {bg.id === 'blur' ? <Monitor className="h-8 w-8 text-white" /> : <Image className="h-8 w-8 text-white" />}
                      </div>
                      <div className="text-white text-sm font-medium">{bg.name}</div>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'security' && (
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-white mb-4">Security Settings</h3>
                
                <div className="space-y-4">
                  <label className="flex items-center justify-between">
                    <div>
                      <span className="text-white font-medium">End-to-End Encryption</span>
                      <p className="text-white/60 text-sm">Encrypt all video and audio streams</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={encryptionEnabled}
                      onChange={(e) => setEncryptionEnabled(e.target.checked)}
                      className="w-5 h-5 rounded bg-white/10 border-white/20"
                    />
                  </label>
                  
                  <label className="flex items-center justify-between">
                    <div>
                      <span className="text-white font-medium">Anti-Spam Protection</span>
                      <p className="text-white/60 text-sm">Prevent message flooding in chat</p>
                    </div>
                    <input
                      type="checkbox"
                      checked={antiSpamEnabled}
                      onChange={(e) => setAntiSpamEnabled(e.target.checked)}
                      className="w-5 h-5 rounded bg-white/10 border-white/20"
                    />
                  </label>
                </div>
              </div>
            )}

            {activeTab === 'general' && (
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-white mb-4">General Settings</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-white text-sm font-medium mb-2">Display Name</label>
                    <input
                      type="text"
                      value={currentUser?.name || ''}
                      className="glass-input w-full"
                      placeholder="Your display name"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-white text-sm font-medium mb-2">Meeting Theme</label>
                    <select className="glass-input w-full">
                      <option value="dark">Dark Theme</option>
                      <option value="light">Light Theme</option>
                      <option value="auto">Auto</option>
                    </select>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-white/10">
          <button
            onClick={onClose}
            className="btn-secondary px-6 py-2"
          >
            Cancel
          </button>
          <button
            onClick={onClose}
            className="btn-primary px-6 py-2"
          >
            Save Changes
          </button>
        </div>
      </div>
    </div>
  )
}
