'use client'

import { useEffect, useRef, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useVideoCallStore } from '@/lib/store'
import { socketManager } from '@/lib/socket'
import { rtcManager } from '@/lib/rtc'
import { VideoGrid } from './VideoGrid'
import { VideoControls } from './VideoControls'
import { Chat } from './Chat'
import { ParticipantsList } from './ParticipantsList'
import { useToast } from '@/components/ui/use-toast'
import { Button } from '@/components/ui/button'
import { Users, MessageCircle, Settings, PhoneOff } from 'lucide-react'

interface VideoCallRoomProps {
  roomId: string
}

export function VideoCallRoom({ roomId }: VideoCallRoomProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [isInitialized, setIsInitialized] = useState(false)
  const [showParticipants, setShowParticipants] = useState(false)
  
  const {
    currentUser,
    participants,
    localStream,
    isChatOpen,
    isConnected,
    setConnected,
    addParticipant,
    removeParticipant,
    updateParticipant,
    setLocalStream,
    toggleChat,
    addMessage,
    reset
  } = useVideoCallStore()

  // Initialize WebRTC and Socket.IO
  useEffect(() => {
    const initializeCall = async () => {
      try {
        // Connect to Socket.IO server
        const socket = socketManager.connect()
        
        if (!socket) {
          throw new Error('Failed to connect to server')
        }

        // Get user media
        const stream = await rtcManager.getUserMedia()
        setLocalStream(stream)

        // Set up RTC signal callback
        rtcManager.setSendSignalCallback((userId, signal) => {
          socketManager.sendSignal(userId, signal)
        })

        // Set up RTC stream callback
        rtcManager.onStream((userId, stream) => {
          updateParticipant(userId, { stream })
        })

        // Set up RTC disconnect callback
        rtcManager.onUserDisconnected((userId) => {
          removeParticipant(userId)
          toast({
            title: 'User disconnected',
            description: `A participant has left the meeting`,
          })
        })

        // Join the room
        if (currentUser) {
          socketManager.joinRoom(roomId, currentUser.id, currentUser.name)
        }

        setIsInitialized(true)
        setConnected(true)

        toast({
          title: 'Connected',
          description: 'Successfully joined the meeting',
        })

      } catch (error) {
        console.error('Failed to initialize call:', error)
        toast({
          title: 'Connection failed',
          description: 'Failed to join the meeting. Please check your camera and microphone permissions.',
          variant: 'destructive'
        })
      }
    }

    if (currentUser && !isInitialized) {
      initializeCall()
    }

    return () => {
      // Cleanup on unmount
      if (isInitialized) {
        socketManager.leaveRoom()
        rtcManager.cleanup()
        reset()
      }
    }
  }, [currentUser, roomId, isInitialized])

  // Set up Socket.IO event listeners
  useEffect(() => {
    if (!isInitialized) return

    const socket = socketManager.getSocket()
    if (!socket) return

    // Handle new user joined
    const handleUserJoined = async (data: { userId: string, userName: string }) => {
      console.log('User joined:', data)
      
      addParticipant({
        id: data.userId,
        name: data.userName,
        isAudioMuted: false,
        isVideoMuted: false,
        isScreenSharing: false
      })

      // Create offer for new user
      try {
        const offer = await rtcManager.createOffer(data.userId)
        socketManager.sendSignal(data.userId, {
          type: 'offer',
          offer
        })
      } catch (error) {
        console.error('Error creating offer:', error)
      }

      toast({
        title: 'User joined',
        description: `${data.userName} joined the meeting`,
      })
    }

    // Handle user left
    const handleUserLeft = (data: { userId: string, userName: string }) => {
      console.log('User left:', data)
      removeParticipant(data.userId)
      rtcManager.removePeerConnection(data.userId)
      
      toast({
        title: 'User left',
        description: `${data.userName} left the meeting`,
      })
    }

    // Handle WebRTC signals
    const handleSignal = async (data: { fromUserId: string, signal: any }) => {
      console.log('Received signal:', data)
      
      try {
        const { fromUserId, signal } = data
        
        switch (signal.type) {
          case 'offer':
            const answer = await rtcManager.createAnswer(fromUserId, signal.offer)
            socketManager.sendSignal(fromUserId, {
              type: 'answer',
              answer
            })
            break
            
          case 'answer':
            await rtcManager.handleAnswer(fromUserId, signal.answer)
            break
            
          case 'ice-candidate':
            await rtcManager.handleIceCandidate(fromUserId, signal.candidate)
            break
        }
      } catch (error) {
        console.error('Error handling signal:', error)
      }
    }

    // Handle chat messages
    const handleChatMessage = (data: { userId: string, userName: string, message: string, timestamp: string }) => {
      addMessage({
        id: Math.random().toString(36).substring(2, 15),
        userId: data.userId,
        userName: data.userName,
        message: data.message,
        timestamp: new Date(data.timestamp)
      })
    }

    // Register event listeners
    socket.on('user-joined', handleUserJoined)
    socket.on('user-left', handleUserLeft)
    socket.on('signal', handleSignal)
    socket.on('chat-message', handleChatMessage)

    return () => {
      socket.off('user-joined', handleUserJoined)
      socket.off('user-left', handleUserLeft)
      socket.off('signal', handleSignal)
      socket.off('chat-message', handleChatMessage)
    }
  }, [isInitialized])

  const handleLeaveCall = () => {
    socketManager.leaveRoom()
    rtcManager.cleanup()
    reset()
    router.push('/')
  }

  if (!isInitialized || !currentUser) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p>Connecting to meeting...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900 flex flex-col">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-white text-lg font-semibold">
              Meeting: {roomId}
            </h1>
            <div className="flex items-center space-x-2 text-gray-300">
              <Users className="h-4 w-4" />
              <span>{participants.size + 1} participants</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowParticipants(!showParticipants)}
              className="text-white hover:bg-gray-700"
            >
              <Users className="h-4 w-4 mr-2" />
              Participants
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleChat}
              className="text-white hover:bg-gray-700"
            >
              <MessageCircle className="h-4 w-4 mr-2" />
              Chat
            </Button>
            
            <Button
              variant="destructive"
              size="sm"
              onClick={handleLeaveCall}
            >
              <PhoneOff className="h-4 w-4 mr-2" />
              Leave
            </Button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex">
        {/* Video area */}
        <div className="flex-1 flex flex-col">
          <VideoGrid />
          <VideoControls />
        </div>
        
        {/* Sidebar */}
        {(isChatOpen || showParticipants) && (
          <div className="w-80 bg-gray-800 border-l border-gray-700 flex flex-col">
            {showParticipants && <ParticipantsList />}
            {isChatOpen && <Chat />}
          </div>
        )}
      </div>
    </div>
  )
}
