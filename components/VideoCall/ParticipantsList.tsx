'use client'

import { useVideoCallStore } from '@/lib/store'
import { Mic, MicOff, Video, VideoOff, Monitor, Crown, MoreVertical, Users } from 'lucide-react'

export function ParticipantsList() {
  const { currentUser, participants } = useVideoCallStore()

  const participantsList = Array.from(participants.values())
  const allParticipants = currentUser ? [currentUser, ...participantsList] : participantsList

  const getStatusIcon = (participant: any) => {
    const icons = []

    if (participant.isAudioMuted) {
      icons.push(<MicOff key="mic" className="h-3 w-3 text-red-400" />)
    } else {
      icons.push(<Mic key="mic" className="h-3 w-3 text-green-400" />)
    }

    if (participant.isVideoMuted) {
      icons.push(<VideoOff key="video" className="h-3 w-3 text-red-400" />)
    } else {
      icons.push(<Video key="video" className="h-3 w-3 text-green-400" />)
    }

    if (participant.isScreenSharing) {
      icons.push(<Monitor key="screen" className="h-3 w-3 text-blue-400" />)
    }

    return icons
  }

  return (
    <div className="chat-container flex flex-col h-full">
      {/* Header */}
      <div className="p-3 border-b border-white/10">
        <h3 className="text-white font-semibold text-sm flex items-center gap-2">
          <Users className="h-4 w-4" />
          Participants ({allParticipants.length})
        </h3>
      </div>

      {/* Participants list */}
      <div className="flex-1 overflow-y-auto p-2 space-y-2">
        {allParticipants.map((participant) => {
          const isCurrentUser = participant.id === currentUser?.id

          return (
            <div
              key={participant.id}
              className="glass-button p-2 hover:bg-white/20 transition-all"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {/* Avatar */}
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">
                      {participant.name.charAt(0).toUpperCase()}
                    </span>
                  </div>

                  {/* Name and status */}
                  <div className="flex flex-col flex-1 min-w-0">
                    <div className="flex items-center space-x-1">
                      <span className="text-white text-xs font-medium truncate">
                        {participant.name}
                        {isCurrentUser && ' (You)'}
                      </span>
                      {isCurrentUser && (
                        <Crown className="h-3 w-3 text-yellow-400 flex-shrink-0" />
                      )}
                    </div>
                    <div className="flex items-center space-x-1">
                      {getStatusIcon(participant)}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                {!isCurrentUser && (
                  <button className="glass-button p-1 text-white/60 hover:text-white">
                    <MoreVertical className="h-3 w-3" />
                  </button>
                )}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
