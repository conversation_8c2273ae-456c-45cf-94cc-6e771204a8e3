'use client'

import { useVideoCallStore } from '@/lib/store'
import { Mic, MicOff, Video, VideoOff, Monitor, Crown, MoreVertical } from 'lucide-react'

export function ParticipantsList() {
  const { currentUser, participants } = useVideoCallStore()

  const participantsList = Array.from(participants.values())
  const allParticipants = currentUser ? [currentUser, ...participantsList] : participantsList

  const getStatusIcon = (participant: any) => {
    const icons = []

    if (participant.isAudioMuted) {
      icons.push(<MicOff key="mic" className="h-3 w-3 text-red-400" />)
    } else {
      icons.push(<Mic key="mic" className="h-3 w-3 text-green-400" />)
    }

    if (participant.isVideoMuted) {
      icons.push(<VideoOff key="video" className="h-3 w-3 text-red-400" />)
    } else {
      icons.push(<Video key="video" className="h-3 w-3 text-green-400" />)
    }

    if (participant.isScreenSharing) {
      icons.push(<Monitor key="screen" className="h-3 w-3 text-blue-400" />)
    }

    return icons
  }

  return (
    <div className="chat-container flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-white/10">
        <h3 className="text-white font-semibold text-lg">
          Participants ({allParticipants.length})
        </h3>
      </div>

      {/* Participants list */}
      <div className="flex-1 overflow-y-auto p-2">
        {allParticipants.map((participant) => {
          const isCurrentUser = participant.id === currentUser?.id

          return (
            <div
              key={participant.id}
              className="glass-button p-3 mb-2 hover:bg-white/20 transition-all"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {/* Avatar */}
                  <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">
                      {participant.name.charAt(0).toUpperCase()}
                    </span>
                  </div>

                  {/* Name and status */}
                  <div className="flex flex-col">
                    <div className="flex items-center space-x-2">
                      <span className="text-white text-sm font-medium">
                        {participant.name}
                        {isCurrentUser && ' (You)'}
                      </span>
                      {isCurrentUser && (
                        <Crown className="h-4 w-4 text-yellow-400" />
                      )}
                    </div>
                    <div className="flex items-center space-x-1 mt-1">
                      {getStatusIcon(participant)}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                {!isCurrentUser && (
                  <button className="glass-button p-2 text-white/60 hover:text-white">
                    <MoreVertical className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
