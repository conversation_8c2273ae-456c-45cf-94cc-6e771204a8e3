'use client'

import { useVideoCallStore } from '@/lib/store'
import { Button } from '@/components/ui/button'
import { Mic, MicOff, Video, VideoOff, Monitor, Crown, MoreVertical } from 'lucide-react'

export function ParticipantsList() {
  const { currentUser, participants } = useVideoCallStore()
  
  const participantsList = Array.from(participants.values())
  const allParticipants = currentUser ? [currentUser, ...participantsList] : participantsList

  const getStatusIcon = (participant: any) => {
    const icons = []
    
    if (participant.isAudioMuted) {
      icons.push(<MicOff key="mic" className="h-3 w-3 text-red-400" />)
    } else {
      icons.push(<Mic key="mic" className="h-3 w-3 text-green-400" />)
    }
    
    if (participant.isVideoMuted) {
      icons.push(<VideoOff key="video" className="h-3 w-3 text-red-400" />)
    } else {
      icons.push(<Video key="video" className="h-3 w-3 text-green-400" />)
    }
    
    if (participant.isScreenSharing) {
      icons.push(<Monitor key="screen" className="h-3 w-3 text-blue-400" />)
    }
    
    return icons
  }

  return (
    <div className="flex flex-col h-1/2 border-b border-gray-700">
      {/* Header */}
      <div className="p-4 border-b border-gray-700">
        <h3 className="text-white font-semibold">
          Participants ({allParticipants.length})
        </h3>
      </div>

      {/* Participants list */}
      <div className="flex-1 overflow-y-auto">
        {allParticipants.map((participant) => {
          const isCurrentUser = participant.id === currentUser?.id
          
          return (
            <div
              key={participant.id}
              className="flex items-center justify-between p-3 hover:bg-gray-700 transition-colors"
            >
              <div className="flex items-center space-x-3">
                {/* Avatar */}
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-semibold">
                    {participant.name.charAt(0).toUpperCase()}
                  </span>
                </div>
                
                {/* Name and status */}
                <div className="flex flex-col">
                  <div className="flex items-center space-x-2">
                    <span className="text-white text-sm font-medium">
                      {participant.name}
                      {isCurrentUser && ' (You)'}
                    </span>
                    {isCurrentUser && (
                      <Crown className="h-3 w-3 text-yellow-400" />
                    )}
                  </div>
                  <div className="flex items-center space-x-1">
                    {getStatusIcon(participant)}
                  </div>
                </div>
              </div>

              {/* Actions */}
              {!isCurrentUser && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-gray-400 hover:text-white p-1"
                >
                  <MoreVertical className="h-4 w-4" />
                </Button>
              )}
            </div>
          )
        })}
      </div>
    </div>
  )
}
