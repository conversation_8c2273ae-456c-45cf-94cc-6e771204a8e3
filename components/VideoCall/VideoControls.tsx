'use client'

import { useState } from 'react'
import { useVideoCallStore } from '@/lib/store'
import { rtcManager } from '@/lib/rtc'
import { Button } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { 
  Mic, 
  MicOff, 
  Video, 
  VideoOff, 
  Monitor, 
  MonitorOff,
  Settings,
  PhoneOff
} from 'lucide-react'

export function VideoControls() {
  const { toast } = useToast()
  const [isScreenSharing, setIsScreenSharing] = useState(false)
  
  const {
    isAudioMuted,
    isVideoMuted,
    toggleAudio,
    toggleVideo,
    toggleSettings
  } = useVideoCallStore()

  const handleToggleAudio = () => {
    toggleAudio()
    toast({
      title: isAudioMuted ? 'Microphone unmuted' : 'Microphone muted',
      description: isAudioMuted ? 'You can now speak' : 'Others cannot hear you',
    })
  }

  const handleToggleVideo = () => {
    toggleVideo()
    toast({
      title: isVideoMuted ? 'Camera turned on' : 'Camera turned off',
      description: isVideoMuted ? 'Others can now see you' : 'Others cannot see you',
    })
  }

  const handleToggleScreenShare = async () => {
    try {
      if (!isScreenSharing) {
        // Start screen sharing
        const screenStream = await rtcManager.getDisplayMedia()
        
        // Replace video track with screen share
        rtcManager.replaceVideoTrack(screenStream)
        
        // Listen for screen share end
        screenStream.getVideoTracks()[0].addEventListener('ended', () => {
          handleStopScreenShare()
        })
        
        setIsScreenSharing(true)
        toast({
          title: 'Screen sharing started',
          description: 'Your screen is now being shared',
        })
      } else {
        handleStopScreenShare()
      }
    } catch (error) {
      console.error('Error toggling screen share:', error)
      toast({
        title: 'Screen sharing failed',
        description: 'Could not start screen sharing. Please try again.',
        variant: 'destructive'
      })
    }
  }

  const handleStopScreenShare = async () => {
    try {
      // Get camera stream back
      const cameraStream = await rtcManager.getUserMedia()
      
      // Replace screen share with camera
      rtcManager.replaceVideoTrack(cameraStream)
      
      setIsScreenSharing(false)
      toast({
        title: 'Screen sharing stopped',
        description: 'Camera is now active',
      })
    } catch (error) {
      console.error('Error stopping screen share:', error)
      toast({
        title: 'Error',
        description: 'Could not switch back to camera',
        variant: 'destructive'
      })
    }
  }

  return (
    <div className="bg-gray-800 border-t border-gray-700 p-4">
      <div className="flex items-center justify-center space-x-4">
        {/* Audio toggle */}
        <Button
          variant={isAudioMuted ? "destructive" : "secondary"}
          size="lg"
          onClick={handleToggleAudio}
          className="rounded-full w-12 h-12 p-0"
        >
          {isAudioMuted ? (
            <MicOff className="h-5 w-5" />
          ) : (
            <Mic className="h-5 w-5" />
          )}
        </Button>

        {/* Video toggle */}
        <Button
          variant={isVideoMuted ? "destructive" : "secondary"}
          size="lg"
          onClick={handleToggleVideo}
          className="rounded-full w-12 h-12 p-0"
        >
          {isVideoMuted ? (
            <VideoOff className="h-5 w-5" />
          ) : (
            <Video className="h-5 w-5" />
          )}
        </Button>

        {/* Screen share toggle */}
        <Button
          variant={isScreenSharing ? "default" : "secondary"}
          size="lg"
          onClick={handleToggleScreenShare}
          className="rounded-full w-12 h-12 p-0"
        >
          {isScreenSharing ? (
            <MonitorOff className="h-5 w-5" />
          ) : (
            <Monitor className="h-5 w-5" />
          )}
        </Button>

        {/* Settings */}
        <Button
          variant="secondary"
          size="lg"
          onClick={toggleSettings}
          className="rounded-full w-12 h-12 p-0"
        >
          <Settings className="h-5 w-5" />
        </Button>
      </div>
    </div>
  )
}
