'use client'

import { useState } from 'react'
import { useVideoCallStore } from '@/lib/store'
import { rtcManager } from '@/lib/rtc'
import {
  Mic,
  MicOff,
  Video,
  VideoOff,
  Monitor,
  MonitorOff,
  Settings,
  PhoneOff
} from 'lucide-react'

export function VideoControls() {
  const [isScreenSharing, setIsScreenSharing] = useState(false)
  const [notification, setNotification] = useState<string | null>(null)

  const {
    isAudioMuted,
    isVideoMuted,
    toggleAudio,
    toggleVideo,
    toggleSettings
  } = useVideoCallStore()

  const showNotification = (message: string) => {
    setNotification(message)
    setTimeout(() => setNotification(null), 2000)
  }

  const handleToggleAudio = () => {
    toggleAudio()
    showNotification(isAudioMuted ? 'Microphone unmuted' : 'Microphone muted')
  }

  const handleToggleVideo = () => {
    toggleVideo()
    showNotification(isVideoMuted ? 'Camera turned on' : 'Camera turned off')
  }

  const handleToggleScreenShare = async () => {
    try {
      if (!isScreenSharing) {
        // Start screen sharing
        const screenStream = await rtcManager.getDisplayMedia()

        // Replace video track with screen share
        rtcManager.replaceVideoTrack(screenStream)

        // Listen for screen share end
        screenStream.getVideoTracks()[0].addEventListener('ended', () => {
          handleStopScreenShare()
        })

        setIsScreenSharing(true)
        showNotification('Screen sharing started')
      } else {
        handleStopScreenShare()
      }
    } catch (error) {
      console.error('Error toggling screen share:', error)
      showNotification('Screen sharing failed')
    }
  }

  const handleStopScreenShare = async () => {
    try {
      // Get camera stream back
      const cameraStream = await rtcManager.getUserMedia()

      // Replace screen share with camera
      rtcManager.replaceVideoTrack(cameraStream)

      setIsScreenSharing(false)
      showNotification('Screen sharing stopped')
    } catch (error) {
      console.error('Error stopping screen share:', error)
      showNotification('Could not switch back to camera')
    }
  }

  return (
    <div className="p-4">
      <div className="video-controls">
        {/* Audio toggle */}
        <button
          onClick={handleToggleAudio}
          className={`control-btn ${isAudioMuted ? 'active' : 'inactive'}`}
          title={isAudioMuted ? 'Unmute microphone' : 'Mute microphone'}
        >
          {isAudioMuted ? (
            <MicOff className="h-6 w-6" />
          ) : (
            <Mic className="h-6 w-6" />
          )}
        </button>

        {/* Video toggle */}
        <button
          onClick={handleToggleVideo}
          className={`control-btn ${isVideoMuted ? 'active' : 'inactive'}`}
          title={isVideoMuted ? 'Turn on camera' : 'Turn off camera'}
        >
          {isVideoMuted ? (
            <VideoOff className="h-6 w-6" />
          ) : (
            <Video className="h-6 w-6" />
          )}
        </button>

        {/* Screen share toggle */}
        <button
          onClick={handleToggleScreenShare}
          className={`control-btn ${isScreenSharing ? 'active' : 'inactive'}`}
          title={isScreenSharing ? 'Stop screen sharing' : 'Share screen'}
        >
          {isScreenSharing ? (
            <MonitorOff className="h-6 w-6" />
          ) : (
            <Monitor className="h-6 w-6" />
          )}
        </button>

        {/* Settings */}
        <button
          onClick={toggleSettings}
          className="control-btn inactive"
          title="Settings"
        >
          <Settings className="h-6 w-6" />
        </button>
      </div>

      {/* Notification */}
      {notification && (
        <div className="fixed bottom-20 left-1/2 transform -translate-x-1/2 glass-dark px-4 py-2 rounded-lg text-white z-50 fade-in">
          {notification}
        </div>
      )}
    </div>
  )
}
