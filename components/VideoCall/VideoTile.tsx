'use client'

import { useEffect, useRef } from 'react'
import { Participant } from '@/lib/store'
import { Mic, MicOff, Video, VideoOff, Monitor } from 'lucide-react'

interface VideoTileProps {
  participant: Participant
  stream?: MediaStream | null
  isLocal: boolean
}

export function VideoTile({ participant, stream, isLocal }: VideoTileProps) {
  const videoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    if (videoRef.current && stream) {
      videoRef.current.srcObject = stream
    }
  }, [stream])

  const hasVideo = stream && stream.getVideoTracks().length > 0 && !participant.isVideoMuted
  const hasAudio = stream && stream.getAudioTracks().length > 0 && !participant.isAudioMuted

  return (
    <div className="video-tile relative">
      {hasVideo ? (
        <video
          ref={videoRef}
          autoPlay
          playsInline
          muted={isLocal} // Mute local video to prevent feedback
          className="w-full h-full object-cover"
        />
      ) : (
        <div className="w-full h-full bg-gray-800 flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-2">
              <span className="text-white text-xl font-semibold">
                {participant.name.charAt(0).toUpperCase()}
              </span>
            </div>
            <p className="text-white text-sm">{participant.name}</p>
          </div>
        </div>
      )}

      {/* Participant info overlay */}
      <div className="absolute bottom-2 left-2 right-2 flex items-center justify-between">
        <div className="bg-black bg-opacity-50 rounded px-2 py-1">
          <span className="text-white text-sm font-medium">
            {isLocal ? 'You' : participant.name}
          </span>
        </div>

        <div className="flex items-center space-x-1">
          {/* Audio indicator */}
          <div className={`p-1 rounded ${hasAudio ? 'bg-green-600' : 'bg-red-600'}`}>
            {hasAudio ? (
              <Mic className="h-3 w-3 text-white" />
            ) : (
              <MicOff className="h-3 w-3 text-white" />
            )}
          </div>

          {/* Video indicator */}
          <div className={`p-1 rounded ${hasVideo ? 'bg-green-600' : 'bg-red-600'}`}>
            {hasVideo ? (
              <Video className="h-3 w-3 text-white" />
            ) : (
              <VideoOff className="h-3 w-3 text-white" />
            )}
          </div>

          {/* Screen sharing indicator */}
          {participant.isScreenSharing && (
            <div className="p-1 rounded bg-blue-600">
              <Monitor className="h-3 w-3 text-white" />
            </div>
          )}
        </div>
      </div>

      {/* Local user indicator */}
      {isLocal && (
        <div className="absolute top-2 left-2">
          <div className="bg-blue-600 text-white text-xs px-2 py-1 rounded">
            You
          </div>
        </div>
      )}
    </div>
  )
}
