'use client'

import { useEffect, useRef } from 'react'
import { Participant } from '@/lib/store'
import { Mi<PERSON>, MicOff, Video, VideoOff, Monitor, Crown } from 'lucide-react'

interface VideoTileProps {
  participant: Participant
  stream?: MediaStream | null
  isLocal: boolean
}

export function VideoTile({ participant, stream, isLocal }: VideoTileProps) {
  const videoRef = useRef<HTMLVideoElement>(null)

  useEffect(() => {
    if (videoRef.current && stream) {
      videoRef.current.srcObject = stream
    }
  }, [stream])

  const hasVideo = stream && stream.getVideoTracks().length > 0 && !participant.isVideoMuted
  const hasAudio = stream && stream.getAudioTracks().length > 0 && !participant.isAudioMuted

  return (
    <div className="video-container relative overflow-hidden">
      {hasVideo ? (
        <video
          ref={videoRef}
          autoPlay
          playsInline
          muted={isLocal} // Mute local video to prevent feedback
          className="w-full h-full object-cover"
        />
      ) : (
        <div className="w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center">
          <div className="text-center">
            <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
              <span className="text-white text-2xl font-bold">
                {participant.name.charAt(0).toUpperCase()}
              </span>
            </div>
            <p className="text-white text-lg font-medium">{participant.name}</p>
            <p className="text-white/60 text-sm">Camera is off</p>
          </div>
        </div>
      )}

      {/* Participant info overlay */}
      <div className="absolute bottom-4 left-4 right-4 flex items-center justify-between">
        <div className="glass-dark px-3 py-2 rounded-lg">
          <div className="flex items-center gap-2">
            {isLocal && <Crown className="h-4 w-4 text-yellow-400" />}
            <span className="text-white font-medium">
              {isLocal ? 'You' : participant.name}
            </span>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Audio indicator */}
          <div className={`p-2 rounded-full ${hasAudio ? 'bg-green-500/80' : 'bg-red-500/80'} backdrop-blur-sm`}>
            {hasAudio ? (
              <Mic className="h-4 w-4 text-white" />
            ) : (
              <MicOff className="h-4 w-4 text-white" />
            )}
          </div>

          {/* Video indicator */}
          <div className={`p-2 rounded-full ${hasVideo ? 'bg-green-500/80' : 'bg-red-500/80'} backdrop-blur-sm`}>
            {hasVideo ? (
              <Video className="h-4 w-4 text-white" />
            ) : (
              <VideoOff className="h-4 w-4 text-white" />
            )}
          </div>

          {/* Screen sharing indicator */}
          {participant.isScreenSharing && (
            <div className="p-2 rounded-full bg-blue-500/80 backdrop-blur-sm">
              <Monitor className="h-4 w-4 text-white" />
            </div>
          )}
        </div>
      </div>

      {/* Local user indicator */}
      {isLocal && (
        <div className="absolute top-4 left-4">
          <div className="glass-dark px-3 py-1 rounded-lg">
            <span className="text-white text-sm font-medium flex items-center gap-1">
              <Crown className="h-3 w-3 text-yellow-400" />
              Host
            </span>
          </div>
        </div>
      )}

      {/* Connection quality indicator */}
      <div className="absolute top-4 right-4">
        <div className="flex space-x-1">
          <div className="w-1 h-3 bg-green-400 rounded-full"></div>
          <div className="w-1 h-4 bg-green-400 rounded-full"></div>
          <div className="w-1 h-5 bg-green-400 rounded-full"></div>
        </div>
      </div>
    </div>
  )
}
