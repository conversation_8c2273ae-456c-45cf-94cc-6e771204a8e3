'use client'

import { useEffect, useRef } from 'react'
import { useVideoCallStore } from '@/lib/store'
import { VideoTile } from './VideoTile'

export function VideoGrid() {
  const { currentUser, participants, localStream } = useVideoCallStore()
  
  const participantsList = Array.from(participants.values())
  const totalParticipants = participantsList.length + 1 // +1 for current user

  // Calculate grid layout
  const getGridClass = (count: number) => {
    if (count === 1) return 'video-grid-1'
    if (count === 2) return 'video-grid-2'
    if (count <= 4) return 'video-grid-4'
    if (count <= 6) return 'video-grid-6'
    return 'video-grid-9'
  }

  return (
    <div className="flex-1 p-4">
      <div className={`video-grid ${getGridClass(totalParticipants)} h-full`}>
        {/* Local user video */}
        {currentUser && (
          <VideoTile
            key={currentUser.id}
            participant={currentUser}
            stream={localStream}
            isLocal={true}
          />
        )}
        
        {/* Remote participants */}
        {participantsList.map((participant) => (
          <VideoTile
            key={participant.id}
            participant={participant}
            stream={participant.stream}
            isLocal={false}
          />
        ))}
      </div>
    </div>
  )
}
