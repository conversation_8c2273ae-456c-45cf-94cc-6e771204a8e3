'use client'

import { useVideoCallStore } from '@/lib/store'
import { VideoTile } from './VideoTile'

export function VideoGrid() {
  const { currentUser, participants, localStream } = useVideoCallStore()

  const participantsList = Array.from(participants.values())
  const totalParticipants = participantsList.length + 1 // +1 for current user

  // Calculate grid layout
  const getGridClass = (count: number) => {
    if (count === 1) return 'grid-cols-1'
    if (count === 2) return 'grid-cols-2'
    if (count <= 4) return 'grid-cols-2'
    if (count <= 6) return 'grid-cols-3'
    return 'grid-cols-3'
  }

  const getGridRows = (count: number) => {
    if (count <= 2) return 'grid-rows-1'
    if (count <= 4) return 'grid-rows-2'
    return 'grid-rows-3'
  }

  return (
    <div className="flex-1 p-4">
      <div className={`
        grid gap-4 h-full
        ${getGridClass(totalParticipants)}
        ${getGridRows(totalParticipants)}
      `}>
        {/* Local user video */}
        {currentUser && (
          <VideoTile
            key={currentUser.id}
            participant={currentUser}
            stream={localStream}
            isLocal={true}
          />
        )}

        {/* Remote participants */}
        {participantsList.map((participant) => (
          <VideoTile
            key={participant.id}
            participant={participant}
            stream={participant.stream}
            isLocal={false}
          />
        ))}
      </div>
    </div>
  )
}
