{"name": "streamit-pro", "version": "1.0.0", "description": "Professional video conferencing platform with advanced features like local recording, background effects, and real-time communication", "main": "streamit-pro.html", "scripts": {"start": "python -m http.server 8000", "serve": "npx http-server -p 8000", "deploy": "deploy.bat"}, "keywords": ["video-conferencing", "webrtc", "react", "recording", "chat", "screen-sharing", "background-effects", "professional", "meeting", "conference"], "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/joelgriiyo/streamit2.git"}, "homepage": "https://joelgriiyo.github.io/streamit2/streamit-pro.html", "bugs": {"url": "https://github.com/joelgriiyo/streamit2/issues"}, "engines": {"node": ">=16.0.0"}}