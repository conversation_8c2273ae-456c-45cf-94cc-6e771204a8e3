@echo off
echo 🚀 StreamIt Pro - GitHub Deployment Script
echo ==========================================

echo.
echo 📁 Initializing Git repository...
git init

echo.
echo 🔗 Adding remote repository...
git remote add origin https://github.com/joelgriiyo/streamit2.git
git remote set-url origin https://github.com/joelgriiyo/streamit2.git

echo.
echo 📋 Staging files...
git add streamit-pro.html
git add README.md
git add .gitignore
git add package.json
git add package-lock.json
git add server.js
git add Procfile
git add .npmrc

echo.
echo 📊 Checking status...
git status

echo.
echo 💾 Creating commit...
git commit -m "🎉 Initial commit: Complete StreamIt Pro video conferencing application

✨ Features implemented:
- HD video conferencing with WebRTC
- Local recording to desktop (.webm format)
- Real-time chat messaging
- Background effects (blur, virtual)
- Device management (multiple cameras/mics)
- Microphone volume testing
- Shareable meeting links
- Professional glass morphism UI
- Name input before meeting start
- Proper media cleanup on call end
- Responsive design for all devices
- Toast notification system
- Settings modal with device switching
- Screen sharing capability
- Meeting duration timer
- Connection quality indicators

🛠 Tech Stack:
- React 18 with hooks and context
- WebRTC API for real-time communication
- MediaRecorder API for local recording
- Web Audio API for volume analysis
- Tailwind CSS with custom animations
- Single-file deployment ready

🚀 Ready for production deployment!

📖 Features beautiful README with:
- Professional badges and shields
- Interactive collapsible sections
- Mermaid architecture diagrams
- Comprehensive documentation
- Troubleshooting guides
- Browser compatibility tables"

echo.
echo 🚀 Pushing to GitHub...
git branch -M main
git push -u origin main

echo.
echo ✅ Deployment complete!
echo 🌐 Your app will be available at: https://github.com/joelgriiyo/streamit2
echo 📱 Enable GitHub Pages for live demo at: https://joelgriiyo.github.io/streamit2/streamit-pro.html

pause
