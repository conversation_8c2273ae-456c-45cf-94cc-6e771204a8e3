'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { VideoCallRoom } from '@/components/VideoCall/VideoCallRoom'
import { useVideoCallStore } from '@/lib/store'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default function RoomPage() {
  const params = useParams()
  const router = useRouter()
  const roomId = params.id as string
  const [userName, setUserName] = useState('')
  const [isJoining, setIsJoining] = useState(false)
  const [hasJoined, setHasJoined] = useState(false)
  
  const { setRoomId, setCurrentUser } = useVideoCallStore()

  useEffect(() => {
    // Check if user name is already stored
    const storedName = localStorage.getItem('userName')
    if (storedName) {
      setUserName(storedName)
      handleJoinRoom(storedName)
    }
  }, [])

  const handleJoinRoom = async (name: string) => {
    if (!name.trim()) {
      alert('Please enter your name')
      return
    }

    setIsJoining(true)
    
    try {
      // Set room ID in store
      setRoomId(roomId)
      
      // Create user object
      const user = {
        id: Math.random().toString(36).substring(2, 15),
        name: name.trim(),
        isAudioMuted: false,
        isVideoMuted: false,
        isScreenSharing: false
      }
      
      // Set current user in store
      setCurrentUser(user)
      
      // Store user name for future sessions
      localStorage.setItem('userName', name.trim())
      
      setHasJoined(true)
    } catch (error) {
      console.error('Error joining room:', error)
      alert('Failed to join room. Please try again.')
    } finally {
      setIsJoining(false)
    }
  }

  const handleNameSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleJoinRoom(userName)
  }

  if (!hasJoined) {
    return (
      <>
        <div className="animated-bg"></div>
        <div className="min-h-screen flex items-center justify-center p-4 relative z-10">
          <div className="glass p-8 max-w-md w-full">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-white mb-2">Join Meeting</h2>
              <p className="text-white/70">
                Meeting ID: <span className="font-mono font-semibold text-purple-300">{roomId}</span>
              </p>
            </div>

            <form onSubmit={handleNameSubmit} className="space-y-4">
              <input
                type="text"
                placeholder="Enter your name"
                value={userName}
                onChange={(e) => setUserName(e.target.value)}
                disabled={isJoining}
                autoFocus
                className="glass-input w-full"
              />

              <div className="flex gap-3">
                <button
                  type="button"
                  onClick={() => router.push('/')}
                  disabled={isJoining}
                  className="btn-secondary flex-1"
                >
                  Back
                </button>
                <button
                  type="submit"
                  disabled={isJoining || !userName.trim()}
                  className="btn-primary flex-1"
                >
                  {isJoining ? 'Joining...' : 'Join Meeting'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </>
    )
  }

  return <VideoCallRoom roomId={roomId} />
}
