'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { VideoCallRoom } from '@/components/VideoCall/VideoCallRoom'
import { useVideoCallStore } from '@/lib/store'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default function RoomPage() {
  const params = useParams()
  const router = useRouter()
  const roomId = params.id as string
  const [userName, setUserName] = useState('')
  const [isJoining, setIsJoining] = useState(false)
  const [hasJoined, setHasJoined] = useState(false)
  
  const { setRoomId, setCurrentUser } = useVideoCallStore()

  useEffect(() => {
    // Check if user name is already stored
    const storedName = localStorage.getItem('userName')
    if (storedName) {
      setUserName(storedName)
      handleJoinRoom(storedName)
    }
  }, [])

  const handleJoinRoom = async (name: string) => {
    if (!name.trim()) {
      alert('Please enter your name')
      return
    }

    setIsJoining(true)
    
    try {
      // Set room ID in store
      setRoomId(roomId)
      
      // Create user object
      const user = {
        id: Math.random().toString(36).substring(2, 15),
        name: name.trim(),
        isAudioMuted: false,
        isVideoMuted: false,
        isScreenSharing: false
      }
      
      // Set current user in store
      setCurrentUser(user)
      
      // Store user name for future sessions
      localStorage.setItem('userName', name.trim())
      
      setHasJoined(true)
    } catch (error) {
      console.error('Error joining room:', error)
      alert('Failed to join room. Please try again.')
    } finally {
      setIsJoining(false)
    }
  }

  const handleNameSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleJoinRoom(userName)
  }

  if (!hasJoined) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Join Meeting</CardTitle>
            <CardDescription>
              Room ID: <span className="font-mono font-semibold">{roomId}</span>
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleNameSubmit} className="space-y-4">
              <div>
                <Input
                  placeholder="Enter your name"
                  value={userName}
                  onChange={(e) => setUserName(e.target.value)}
                  disabled={isJoining}
                  autoFocus
                />
              </div>
              <div className="flex gap-2">
                <Button 
                  type="submit" 
                  className="flex-1" 
                  disabled={isJoining || !userName.trim()}
                >
                  {isJoining ? 'Joining...' : 'Join Meeting'}
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => router.push('/')}
                  disabled={isJoining}
                >
                  Back
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    )
  }

  return <VideoCallRoom roomId={roomId} />
}
