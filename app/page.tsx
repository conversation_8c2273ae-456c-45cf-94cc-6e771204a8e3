'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Video, Users, Shield, Zap } from 'lucide-react'

export default function HomePage() {
  const [roomId, setRoomId] = useState('')
  const [userName, setUserName] = useState('')
  const router = useRouter()

  const generateRoomId = () => {
    const id = Math.random().toString(36).substring(2, 15)
    setRoomId(id)
  }

  const joinRoom = () => {
    if (!roomId.trim() || !userName.trim()) {
      alert('Please enter both room ID and your name')
      return
    }
    
    // Store user name in localStorage for the session
    localStorage.setItem('userName', userName)
    router.push(`/room/${roomId}`)
  }

  const createRoom = () => {
    if (!userName.trim()) {
      alert('Please enter your name')
      return
    }
    
    const newRoomId = Math.random().toString(36).substring(2, 15)
    localStorage.setItem('userName', userName)
    router.push(`/room/${newRoomId}`)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <Video className="h-12 w-12 text-blue-600 mr-3" />
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
              StreamIt Pro
            </h1>
          </div>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Professional video conferencing platform with high-quality video, screen sharing, and real-time chat
          </p>
        </div>

        {/* Features */}
        <div className="grid md:grid-cols-3 gap-6 mb-12">
          <Card>
            <CardHeader>
              <Users className="h-8 w-8 text-blue-600 mb-2" />
              <CardTitle>Multi-Participant</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Connect with multiple participants in high-quality video calls
              </CardDescription>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <Shield className="h-8 w-8 text-green-600 mb-2" />
              <CardTitle>Secure & Private</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                End-to-end encrypted communications with WebRTC technology
              </CardDescription>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <Zap className="h-8 w-8 text-yellow-600 mb-2" />
              <CardTitle>Lightning Fast</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Optimized for performance with minimal latency and high quality
              </CardDescription>
            </CardContent>
          </Card>
        </div>

        {/* Main Action Cards */}
        <div className="max-w-4xl mx-auto grid md:grid-cols-2 gap-8">
          {/* Join Room */}
          <Card>
            <CardHeader>
              <CardTitle>Join a Meeting</CardTitle>
              <CardDescription>
                Enter a room ID to join an existing meeting
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Input
                placeholder="Your name"
                value={userName}
                onChange={(e) => setUserName(e.target.value)}
              />
              <div className="flex gap-2">
                <Input
                  placeholder="Room ID"
                  value={roomId}
                  onChange={(e) => setRoomId(e.target.value)}
                  className="flex-1"
                />
                <Button variant="outline" onClick={generateRoomId}>
                  Generate
                </Button>
              </div>
              <Button onClick={joinRoom} className="w-full" size="lg">
                Join Meeting
              </Button>
            </CardContent>
          </Card>

          {/* Create Room */}
          <Card>
            <CardHeader>
              <CardTitle>Start a Meeting</CardTitle>
              <CardDescription>
                Create a new room and invite others to join
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Input
                placeholder="Your name"
                value={userName}
                onChange={(e) => setUserName(e.target.value)}
              />
              <div className="text-sm text-gray-600 dark:text-gray-400">
                A unique room ID will be generated for you
              </div>
              <Button onClick={createRoom} className="w-full" size="lg">
                Start New Meeting
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Footer */}
        <div className="text-center mt-12 text-gray-500 dark:text-gray-400">
          <p>Built with Next.js, TypeScript, TailwindCSS, and WebRTC</p>
        </div>
      </div>
    </div>
  )
}
