import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

export interface Participant {
  id: string
  name: string
  stream?: MediaStream
  isAudioMuted: boolean
  isVideoMuted: boolean
  isScreenSharing: boolean
}

export interface ChatMessage {
  id: string
  userId: string
  userName: string
  message: string
  timestamp: Date
}

interface VideoCallState {
  // Room state
  roomId: string | null
  isConnected: boolean
  
  // User state
  currentUser: Participant | null
  participants: Map<string, Participant>
  
  // Media state
  localStream: MediaStream | null
  isAudioMuted: boolean
  isVideoMuted: boolean
  isScreenSharing: boolean
  
  // Chat state
  messages: ChatMessage[]
  unreadCount: number
  
  // UI state
  isChatOpen: boolean
  isSettingsOpen: boolean
  
  // Actions
  setRoomId: (roomId: string) => void
  setConnected: (connected: boolean) => void
  setCurrentUser: (user: Participant) => void
  addParticipant: (participant: Participant) => void
  removeParticipant: (participantId: string) => void
  updateParticipant: (participantId: string, updates: Partial<Participant>) => void
  setLocalStream: (stream: MediaStream | null) => void
  toggleAudio: () => void
  toggleVideo: () => void
  toggleScreenShare: () => void
  addMessage: (message: ChatMessage) => void
  clearUnreadCount: () => void
  toggleChat: () => void
  toggleSettings: () => void
  reset: () => void
}

const initialState = {
  roomId: null,
  isConnected: false,
  currentUser: null,
  participants: new Map(),
  localStream: null,
  isAudioMuted: false,
  isVideoMuted: false,
  isScreenSharing: false,
  messages: [],
  unreadCount: 0,
  isChatOpen: false,
  isSettingsOpen: false,
}

export const useVideoCallStore = create<VideoCallState>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      setRoomId: (roomId) => set({ roomId }),
      
      setConnected: (isConnected) => set({ isConnected }),
      
      setCurrentUser: (currentUser) => set({ currentUser }),
      
      addParticipant: (participant) => set((state) => {
        const newParticipants = new Map(state.participants)
        newParticipants.set(participant.id, participant)
        return { participants: newParticipants }
      }),
      
      removeParticipant: (participantId) => set((state) => {
        const newParticipants = new Map(state.participants)
        newParticipants.delete(participantId)
        return { participants: newParticipants }
      }),
      
      updateParticipant: (participantId, updates) => set((state) => {
        const newParticipants = new Map(state.participants)
        const participant = newParticipants.get(participantId)
        if (participant) {
          newParticipants.set(participantId, { ...participant, ...updates })
        }
        return { participants: newParticipants }
      }),
      
      setLocalStream: (localStream) => set({ localStream }),
      
      toggleAudio: () => set((state) => {
        const newMuted = !state.isAudioMuted
        if (state.localStream) {
          state.localStream.getAudioTracks().forEach(track => {
            track.enabled = !newMuted
          })
        }
        return { isAudioMuted: newMuted }
      }),
      
      toggleVideo: () => set((state) => {
        const newMuted = !state.isVideoMuted
        if (state.localStream) {
          state.localStream.getVideoTracks().forEach(track => {
            track.enabled = !newMuted
          })
        }
        return { isVideoMuted: newMuted }
      }),
      
      toggleScreenShare: () => set((state) => ({
        isScreenSharing: !state.isScreenSharing
      })),
      
      addMessage: (message) => set((state) => ({
        messages: [...state.messages, message],
        unreadCount: state.isChatOpen ? state.unreadCount : state.unreadCount + 1
      })),
      
      clearUnreadCount: () => set({ unreadCount: 0 }),
      
      toggleChat: () => set((state) => ({
        isChatOpen: !state.isChatOpen,
        unreadCount: !state.isChatOpen ? 0 : state.unreadCount
      })),
      
      toggleSettings: () => set((state) => ({
        isSettingsOpen: !state.isSettingsOpen
      })),
      
      reset: () => set(initialState),
    }),
    {
      name: 'video-call-store',
    }
  )
)
