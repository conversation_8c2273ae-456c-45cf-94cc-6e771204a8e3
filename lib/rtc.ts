// WebRTC configuration
const RTC_CONFIG: RTCConfiguration = {
  iceServers: [
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' },
  ],
}

// High-quality video constraints
export const VIDEO_CONSTRAINTS: MediaStreamConstraints = {
  video: {
    width: { ideal: 1280, max: 1920 },
    height: { ideal: 720, max: 1080 },
    frameRate: { ideal: 30, max: 60 },
    facingMode: 'user'
  },
  audio: {
    echoCancellation: true,
    noiseSuppression: true,
    autoGainControl: true,
    sampleRate: 48000
  }
}

export const SCREEN_SHARE_CONSTRAINTS: DisplayMediaStreamConstraints = {
  video: {
    width: { ideal: 1920, max: 3840 },
    height: { ideal: 1080, max: 2160 },
    frameRate: { ideal: 30, max: 60 }
  },
  audio: true
}

export class RTCManager {
  private peerConnections: Map<string, RTCPeerConnection> = new Map()
  private localStream: MediaStream | null = null
  private onStreamCallback?: (userId: string, stream: MediaStream) => void
  private onUserDisconnectedCallback?: (userId: string) => void

  constructor() {
    this.setupEventHandlers()
  }

  private setupEventHandlers() {
    // Handle page unload
    window.addEventListener('beforeunload', () => {
      this.cleanup()
    })
  }

  async getUserMedia(constraints: MediaStreamConstraints = VIDEO_CONSTRAINTS): Promise<MediaStream> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia(constraints)
      this.localStream = stream
      return stream
    } catch (error) {
      console.error('Error accessing media devices:', error)
      throw new Error('Failed to access camera/microphone')
    }
  }

  async getDisplayMedia(): Promise<MediaStream> {
    try {
      const stream = await navigator.mediaDevices.getDisplayMedia(SCREEN_SHARE_CONSTRAINTS)
      return stream
    } catch (error) {
      console.error('Error accessing screen share:', error)
      throw new Error('Failed to access screen sharing')
    }
  }

  createPeerConnection(userId: string): RTCPeerConnection {
    const pc = new RTCPeerConnection(RTC_CONFIG)

    // Add local stream tracks
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => {
        pc.addTrack(track, this.localStream!)
      })
    }

    // Handle incoming stream
    pc.ontrack = (event) => {
      const [remoteStream] = event.streams
      if (this.onStreamCallback) {
        this.onStreamCallback(userId, remoteStream)
      }
    }

    // Handle ICE candidates
    pc.onicecandidate = (event) => {
      if (event.candidate) {
        // Send ICE candidate to remote peer via signaling
        this.sendSignal(userId, {
          type: 'ice-candidate',
          candidate: event.candidate
        })
      }
    }

    // Handle connection state changes
    pc.onconnectionstatechange = () => {
      console.log(`Connection state for ${userId}:`, pc.connectionState)
      
      if (pc.connectionState === 'disconnected' || pc.connectionState === 'failed') {
        this.removePeerConnection(userId)
        if (this.onUserDisconnectedCallback) {
          this.onUserDisconnectedCallback(userId)
        }
      }
    }

    this.peerConnections.set(userId, pc)
    return pc
  }

  async createOffer(userId: string): Promise<RTCSessionDescriptionInit> {
    const pc = this.peerConnections.get(userId) || this.createPeerConnection(userId)
    
    const offer = await pc.createOffer({
      offerToReceiveAudio: true,
      offerToReceiveVideo: true
    })
    
    await pc.setLocalDescription(offer)
    return offer
  }

  async createAnswer(userId: string, offer: RTCSessionDescriptionInit): Promise<RTCSessionDescriptionInit> {
    const pc = this.peerConnections.get(userId) || this.createPeerConnection(userId)
    
    await pc.setRemoteDescription(offer)
    const answer = await pc.createAnswer()
    await pc.setLocalDescription(answer)
    
    return answer
  }

  async handleAnswer(userId: string, answer: RTCSessionDescriptionInit): Promise<void> {
    const pc = this.peerConnections.get(userId)
    if (pc) {
      await pc.setRemoteDescription(answer)
    }
  }

  async handleIceCandidate(userId: string, candidate: RTCIceCandidateInit): Promise<void> {
    const pc = this.peerConnections.get(userId)
    if (pc) {
      await pc.addIceCandidate(candidate)
    }
  }

  removePeerConnection(userId: string): void {
    const pc = this.peerConnections.get(userId)
    if (pc) {
      pc.close()
      this.peerConnections.delete(userId)
    }
  }

  replaceVideoTrack(newStream: MediaStream): void {
    const videoTrack = newStream.getVideoTracks()[0]
    
    this.peerConnections.forEach(async (pc) => {
      const sender = pc.getSenders().find(s => 
        s.track && s.track.kind === 'video'
      )
      
      if (sender && videoTrack) {
        await sender.replaceTrack(videoTrack)
      }
    })
  }

  onStream(callback: (userId: string, stream: MediaStream) => void): void {
    this.onStreamCallback = callback
  }

  onUserDisconnected(callback: (userId: string) => void): void {
    this.onUserDisconnectedCallback = callback
  }

  private sendSignal(userId: string, signal: any): void {
    // This will be implemented by the component using RTCManager
    // to send signals via Socket.IO
  }

  setSendSignalCallback(callback: (userId: string, signal: any) => void): void {
    this.sendSignal = callback
  }

  cleanup(): void {
    // Close all peer connections
    this.peerConnections.forEach(pc => pc.close())
    this.peerConnections.clear()

    // Stop local stream
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop())
      this.localStream = null
    }
  }

  getLocalStream(): MediaStream | null {
    return this.localStream
  }

  getPeerConnection(userId: string): RTCPeerConnection | undefined {
    return this.peerConnections.get(userId)
  }
}

export const rtcManager = new RTCManager()
