// Main entry point for StreamIt Pro
console.log('StreamIt Pro initializing...');

// Initialize the application when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', () => {
    const appElement = document.getElementById('app');
    if (appElement) {
        appElement.innerHTML = `
            <header>
                <h1>StreamIt Pro</h1>
                <p>Professional Video Conferencing</p>
            </header>
            <main>
                <div id="meeting-container">
                    <div id="local-video-container">
                        <video id="local-video" autoplay muted playsinline></video>
                    </div>
                    <div id="remote-videos"></div>
                </div>
                <div id="controls">
                    <button id="start-meeting">Start Meeting</button>
                    <button id="join-meeting" disabled>Join Meeting</button>
                    <button id="toggle-audio" disabled>Mute</button>
                    <button id="toggle-video" disabled>Stop Video</button>
                    <button id="screen-share" disabled>Share Screen</button>
                </div>
            </main>
            <footer>
                <p>© 2023 StreamIt Pro. All rights reserved.</p>
            </footer>
        `;
    }

    // Initialize the application
    initializeApp();
});

async function initializeApp() {
    try {
        // Request camera and microphone access
        const stream = await navigator.mediaDevices.getUserMedia({
            audio: true,
            video: true
        });

        // Display local video stream
        const localVideo = document.getElementById('local-video') as HTMLVideoElement;
        if (localVideo) {
            localVideo.srcObject = stream;
        }

        // Enable meeting controls
        enableControls();
        
        console.log('StreamIt Pro initialized successfully');
    } catch (error) {
        console.error('Error initializing StreamIt Pro:', error);
        alert('Error accessing media devices. Please ensure you have granted the necessary permissions.');
    }
}

function enableControls() {
    const buttons = [
        'start-meeting',
        'join-meeting',
        'toggle-audio',
        'toggle-video',
        'screen-share'
    ];

    buttons.forEach(buttonId => {
        const button = document.getElementById(buttonId) as HTMLButtonElement;
        if (button) {
            button.disabled = false;
        }
    });
}
